<?php
namespace Auth;

use think\facade\Request;
use app\admin\model\Role;

// 配置文件增加设置
// USER_AUTH_ON 是否需要认证
// USER_AUTH_TYPE 认证类型
// USER_AUTH_KEY 认证识别号
// NOT_AUTH_MODULE 无需认证模块
// USER_AUTH_GATEWAY 认证网关
//
// AUTH_ROLE_MODEL 角色表名称
// AUTH_ADMIN_MODEL 用户表名称
// AUTH_AUTH_RULE_MODEL 权限表名称

class Auth 
{
    /**
     * 是否登录
     * <AUTHOR>
     * @dateTime 2016-02-29T11:17:59+0800
     * @return   [type]                   [description]
     */
    public static function checkLogin()
    {
        //检查当前操作是否需要认证
        if (self::checkAccess()) {
            //检查认证识别号
            $user_auth_key = Config('user_auth_key');
            $isLogin = empty($user_auth_key) ? null : session($user_auth_key);
            if (empty($isLogin)) {
                return false;
            }
        }
        return true;
    }

    /**
     * 是否登录 用于登录页面
     * <AUTHOR>
     * @dateTime 2016-02-18T13:51:59+0800
     * @return   boolean                  [description]
     */
    public static function isLogin()
    {
        if (!session(Config('user_auth_key'))) {
            return false;
        }
        return true;
    }

    /**
     * 是否需要验证
     * <AUTHOR>
     * @dateTime 2016-02-29T11:17:34+0800
     * @param    string                   $value [description]
     * @return   [type]                          [description]
     */
    public static function checkAccess()
    {
        $controller = Request::controller();
        //如果项目要求认证，并且当前模块需要认证，则进行权限认证
        if (Config('user_auth_on')) {
            if (Config('not_auth_module') == '') {
                return true;
            }

            $notAuthModules = explode(',', strtolower(Config('not_auth_module')));
            if (!in_array(strtolower($controller), $notAuthModules)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 权限认证的过滤器方法
     * <AUTHOR>
     * @dateTime 2016-02-29T11:17:51+0800
     * @param    [type]                   $appName [description]
     */
    public static function AccessDecision()
    {
        $module = Request::module();
        $controller = Request::controller();
        $action = Request::action();
        //检查是否需要认证
        if (self::checkAccess()) {
            //存在认证识别号，则进行进一步的访问决策
            $accessGuid = md5(strtolower($module . '/' . $controller . '/' . $action));

            if (Config('user_auth_type') == 2) {
                //加强验证和即时验证模式 更加安全 后台权限修改可以即时生效
                $accessList = self::getAccessList(session(Config('user_auth_key')));
            } else {
                // 如果是管理员或者当前操作已经认证过，无需再次认证
                if (session($accessGuid) === true) {
                    return true;
                } else if (session($accessGuid) === false) {
                    return false;
                }
                //登录验证模式，比较登录后保存的权限访问列表
                $accessList = session('_ACCESS_LIST');
            }
            //判断是否为组件化模式，如果是，验证其全模块名
            if (!isset($accessList[$accessGuid])) {
                session($accessGuid, false);
                return false;
            } else {
                session($accessGuid, true);
            }
        }
        return true;
    }

    /**
     * =========================================================================
     * 重写获取权限列表
     * =========================================================================
     * @param $authId SESSION admin.id
     * @return array
     * <AUTHOR>
     */
    public static function getAccessList($authId)
    {
        $module = Request::module();
        $accessList = array();

        $adminRow = model(Config('auth_admin_model'))->where('id',$authId)->find();
        if (empty($adminRow)) {
            return $accessList;
        }
        //获取权限组
        $roleRow = Role::get($adminRow['role_id']);
        $roles = $roleRow->AuthRule;
        $roles = json_decode(json_encode($roles),true);
        $roleRow['rules'] = $roles;

        if (empty($roleRow) || !isset($roleRow['rules']) || empty($roleRow['rules'])) {
            return $accessList;
        }

        foreach ($roleRow['rules'] as $value) {
            $accessList[md5(strtolower($module . '/' . $value['name']))] = $value;
        }

        return $accessList;
    }

    /**
     * 保存Access
     * <AUTHOR>
     * @dateTime 2016-02-29T11:57:37+0800
     * @param    [type]                   $authId [description]
     * @return   [type]                           [description]
     */
    public static function saveAccessList($authId)
    {
        $accessList = self::getAccessList($authId);

        session('_ACCESS_LIST', $accessList);
    }
}
