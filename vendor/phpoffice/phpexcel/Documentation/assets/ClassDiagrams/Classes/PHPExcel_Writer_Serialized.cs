using System;
using System.Collections.Generic;
using System.Text;

namespace ClassDiagrams
{
    public class PHPExcel_Writer_Serialized : PHPExcel_Writer_IWriter
    {
        #region IWriter Members

        public PHPExcel writes
        {
            get
            {
                throw new Exception("The method or operation is not implemented.");
            }
            set
            {
                throw new Exception("The method or operation is not implemented.");
            }
        }

        #endregion
    }

    public class PHPExcel_Writer_CSV : PHPExcel_Writer_IWriter
    {
        #region IWriter Members

        public PHPExcel writes
        {
            get
            {
                throw new Exception("The method or operation is not implemented.");
            }
            set
            {
                throw new Exception("The method or operation is not implemented.");
            }
        }

        #endregion
    }

    public class PHPExcel_Writer_Excel5 : PHPExcel_Writer_IWriter
    {
        #region IWriter Members

        public PHPExcel writes
        {
            get
            {
                throw new Exception("The method or operation is not implemented.");
            }
            set
            {
                throw new Exception("The method or operation is not implemented.");
            }
        }

        #endregion
    }

    public class PHPExcel_Writer_HTML : PHPExcel_Writer_IWriter
    {
        #region IWriter Members

        public PHPExcel writes
        {
            get
            {
                throw new Exception("The method or operation is not implemented.");
            }
            set
            {
                throw new Exception("The method or operation is not implemented.");
            }
        }

        #endregion
    }
}
