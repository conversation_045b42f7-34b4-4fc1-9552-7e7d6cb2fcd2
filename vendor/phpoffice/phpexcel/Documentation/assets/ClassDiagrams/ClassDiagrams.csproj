﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003" ToolsVersion="3.5">
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProductVersion>8.0.50727</ProductVersion>
    <SchemaVersion>2.0</SchemaVersion>
    <ProjectGuid>{CC8CD7D2-8EFF-48E5-A17A-C1C482744D31}</ProjectGuid>
    <OutputType>Exe</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>ClassDiagrams</RootNamespace>
    <AssemblyName>ClassDiagrams</AssemblyName>
    <FileUpgradeFlags>
    </FileUpgradeFlags>
    <OldToolsVersion>2.0</OldToolsVersion>
    <UpgradeBackupLocation>
    </UpgradeBackupLocation>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <Import Project="$(MSBuildBinPath)\Microsoft.CSharp.targets" />
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it. 
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
  <ItemGroup>
    <None Include="Architecture.cd" />
    <None Include="ReaderWriter.cd" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="Classes\IReader.cs" />
    <Compile Include="Classes\IWriter.cs" />
    <Compile Include="Classes\PHPExcel_IOFactory.cs" />
    <Compile Include="Classes\Worksheet.cs" />
    <Compile Include="Classes\PHPExcel.cs" />
    <Compile Include="Classes\PHPExcel_Reader_Excel2007.cs" />
    <Compile Include="Classes\PHPExcel_Reader_Serialized.cs" />
    <Compile Include="Classes\PHPExcel_Writer_Excel2007.cs" />
    <Compile Include="Classes\PHPExcel_Writer_Serialized.cs" />
    <Compile Include="Classes\PHPExcel_Reader_Excel5.cs" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="Exports\Architecture.png" />
    <Content Include="Exports\ReaderWriter.png" />
  </ItemGroup>
</Project>