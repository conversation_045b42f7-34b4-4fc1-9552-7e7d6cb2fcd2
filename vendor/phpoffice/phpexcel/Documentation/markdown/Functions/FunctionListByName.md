#A

    Excel Function      | Category                       | PHPExcel Function
    --------------------|--------------------------------|-------------------------------------------
    ABS                 | CATEGORY_MATH_AND_TRIG         | abs
    ACCRINT             | CATEGORY_FINANCIAL             | PHPExcel_Calculation_Financial::ACCRINT
    ACCRINTM            | CATEGORY_FINANCIAL             | PHPExcel_Calculation_Financial::ACCRINTM
    ACOS                | CATEGORY_MATH_AND_TRIG         | acos
    ACOSH               | CATEGORY_MATH_AND_TRIG         | acosh
    ADDRESS             | CATEGORY_LOOKUP_AND_REFERENCE  | PHPExcel_Calculation_LookupRef::CELL_ADDRESS
    AMORDEGRC           | CATEGORY_FINANCIAL             | PHPExcel_Calculation_Financial::AMORDEGR<PERSON>
    AMORLINC            | CATEGORY_FINANCIAL             | PHPExcel_Calculation_Financial::AMORLINC
    AND                 | CATEGORY_LOGICAL               | PHPExcel_Calculation_Logical::LOGICAL_AND
    AREAS               | CATEGORY_LOOKUP_AND_REFERENCE  | **\*\*\*  Not yet Implemented**
    ASC                 | CATEGORY_TEXT_AND_DATA         | **\*\*\*  Not yet Implemented**
    ASIN                | CATEGORY_MATH_AND_TRIG         | asin
    ASINH               | CATEGORY_MATH_AND_TRIG         | asinh
    ATAN                | CATEGORY_MATH_AND_TRIG         | atan
    ATAN2               | CATEGORY_MATH_AND_TRIG         | PHPExcel_Calculation_MathTrig::REVERSE_ATAN2
    ATANH               | CATEGORY_MATH_AND_TRIG         | atanh
    AVEDEV              | CATEGORY_STATISTICAL           | PHPExcel_Calculation_Statistical::AVEDEV
    AVERAGE             | CATEGORY_STATISTICAL           | PHPExcel_Calculation_Statistical::AVERAGE
    AVERAGEA            | CATEGORY_STATISTICAL           | PHPExcel_Calculation_Statistical::AVERAGEA
    AVERAGEIF           | CATEGORY_STATISTICAL           | PHPExcel_Calculation_Statistical::AVERAGEIF
    AVERAGEIFS          | CATEGORY_STATISTICAL           | **\*\*\*  Not yet Implemented**
  
#B
  
    Excel Function      | Category                       | PHPExcel Function
    --------------------|--------------------------------|-------------------------------------------
    BAHTTEXT            | CATEGORY_TEXT_AND_DATA         | **\*\*\*  Not yet Implemented**
    BESSELI             | CATEGORY_ENGINEERING           | PHPExcel_Calculation_Engineering::BESSELI
    BESSELJ             | CATEGORY_ENGINEERING           | PHPExcel_Calculation_Engineering::BESSELJ
    BESSELK             | CATEGORY_ENGINEERING           | PHPExcel_Calculation_Engineering::BESSELK
    BESSELY             | CATEGORY_ENGINEERING           | PHPExcel_Calculation_Engineering::BESSELY
    BETADIST            | CATEGORY_STATISTICAL           | PHPExcel_Calculation_Statistical::BETADIST
    BETAINV             | CATEGORY_STATISTICAL           | PHPExcel_Calculation_Statistical::BETAINV
    BIN2DEC             | CATEGORY_ENGINEERING           | PHPExcel_Calculation_Engineering::BINTODEC
    BIN2HEX             | CATEGORY_ENGINEERING           | PHPExcel_Calculation_Engineering::BINTOHEX
    BIN2OCT             | CATEGORY_ENGINEERING           | PHPExcel_Calculation_Engineering::BINTOOCT
    BINOMDIST           | CATEGORY_STATISTICAL           | PHPExcel_Calculation_Statistical::BINOMDIST

#C

    Excel Function      | Category                       | PHPExcel Function
    --------------------|--------------------------------|-------------------------------------------
    CEILING             | CATEGORY_MATH_AND_TRIG         | PHPExcel_Calculation_MathTrig::CEILING
    CELL                | CATEGORY_INFORMATION           | **\*\*\*  Not yet Implemented**
    CHAR                | CATEGORY_TEXT_AND_DATA         | PHPExcel_Calculation_TextData::CHARACTER
    CHIDIST             | CATEGORY_STATISTICAL           | PHPExcel_Calculation_Statistical::CHIDIST
    CHIINV              | CATEGORY_STATISTICAL           | PHPExcel_Calculation_Statistical::CHIINV
    CHITEST             | CATEGORY_STATISTICAL           | **\*\*\*  Not yet Implemented**
    CHOOSE              | CATEGORY_LOOKUP_AND_REFERENCE  | PHPExcel_Calculation_LookupRef::CHOOSE
    CLEAN               | CATEGORY_TEXT_AND_DATA         | PHPExcel_Calculation_TextData::TRIMNONPRINTABLE
    CODE                | CATEGORY_TEXT_AND_DATA         | PHPExcel_Calculation_TextData::ASCIICODE
    COLUMN              | CATEGORY_LOOKUP_AND_REFERENCE  | PHPExcel_Calculation_LookupRef::COLUMN
    COLUMNS             | CATEGORY_LOOKUP_AND_REFERENCE  | PHPExcel_Calculation_LookupRef::COLUMNS
    COMBIN              | CATEGORY_MATH_AND_TRIG         | PHPExcel_Calculation_MathTrig::COMBIN
    COMPLEX             | CATEGORY_ENGINEERING           | PHPExcel_Calculation_Engineering::COMPLEX
    CONCATENATE         | CATEGORY_TEXT_AND_DATA         | PHPExcel_Calculation_TextData::CONCATENATE
    CONFIDENCE          | CATEGORY_STATISTICAL           | PHPExcel_Calculation_Statistical::CONFIDENCE
    CONVERT             | CATEGORY_ENGINEERING           | PHPExcel_Calculation_Engineering::CONVERTUOM
    CORREL              | CATEGORY_STATISTICAL           | PHPExcel_Calculation_Statistical::CORREL
    COS                 | CATEGORY_MATH_AND_TRIG         | cos
    COSH                | CATEGORY_MATH_AND_TRIG         | cosh
    COUNT               | CATEGORY_STATISTICAL           | PHPExcel_Calculation_Statistical::COUNT
    COUNTA              | CATEGORY_STATISTICAL           | PHPExcel_Calculation_Statistical::COUNTA
    COUNTBLANK          | CATEGORY_STATISTICAL           | PHPExcel_Calculation_Statistical::COUNTBLANK
    COUNTIF             | CATEGORY_STATISTICAL           | PHPExcel_Calculation_Statistical::COUNTIF
    COUNTIFS            | CATEGORY_STATISTICAL           | **\*\*\*  Not yet Implemented**
    COUPDAYBS           | CATEGORY_FINANCIAL             | PHPExcel_Calculation_Financial::COUPDAYBS
    COUPDAYS            | CATEGORY_FINANCIAL             | PHPExcel_Calculation_Financial::COUPDAYS
    COUPDAYSNC          | CATEGORY_FINANCIAL             | PHPExcel_Calculation_Financial::COUPDAYSNC
    COUPNCD             | CATEGORY_FINANCIAL             | PHPExcel_Calculation_Financial::COUPNCD
    COUPNUM             | CATEGORY_FINANCIAL             | PHPExcel_Calculation_Financial::COUPNUM
    COUPPCD             | CATEGORY_FINANCIAL             | PHPExcel_Calculation_Financial::COUPPCD
    COVAR               | CATEGORY_STATISTICAL           | PHPExcel_Calculation_Statistical::COVAR
    CRITBINOM           | CATEGORY_STATISTICAL           | PHPExcel_Calculation_Statistical::CRITBINOM
    CUBEKPIMEMBER       | CATEGORY_CUBE                  | **\*\*\*  Not yet Implemented**
    CUBEMEMBER          | CATEGORY_CUBE                  | **\*\*\*  Not yet Implemented**
    CUBEMEMBERPROPERTY  | CATEGORY_CUBE                  | **\*\*\*  Not yet Implemented**
    CUBERANKEDMEMBER    | CATEGORY_CUBE                  | **\*\*\*  Not yet Implemented**
    CUBESET             | CATEGORY_CUBE                  | **\*\*\*  Not yet Implemented**
    CUBESETCOUNT        | CATEGORY_CUBE                  | **\*\*\*  Not yet Implemented**
    CUBEVALUE           | CATEGORY_CUBE                  | **\*\*\*  Not yet Implemented**
    CUMIPMT             | CATEGORY_FINANCIAL             | PHPExcel_Calculation_Financial::CUMIPMT
    CUMPRINC            | CATEGORY_FINANCIAL             | PHPExcel_Calculation_Financial::CUMPRINC

#D

    Excel Function      | Category                       | PHPExcel Function
    --------------------|--------------------------------|-------------------------------------------
    DATE                | CATEGORY_DATE_AND_TIME         | PHPExcel_Calculation_DateTime::DATE
    DATEDIF             | CATEGORY_DATE_AND_TIME         | PHPExcel_Calculation_DateTime::DATEDIF
    DATEVALUE           | CATEGORY_DATE_AND_TIME         | PHPExcel_Calculation_DateTime::DATEVALUE
    DAVERAGE            | CATEGORY_DATABASE              | PHPExcel_Calculation_Database::DAVERAGE
    DAY                 | CATEGORY_DATE_AND_TIME         | PHPExcel_Calculation_DateTime::DAYOFMONTH
    DAYS360             | CATEGORY_DATE_AND_TIME         | PHPExcel_Calculation_DateTime::DAYS360
    DB                  | CATEGORY_FINANCIAL             | PHPExcel_Calculation_Financial::DB
    DCOUNT              | CATEGORY_DATABASE              | PHPExcel_Calculation_Database::DCOUNT
    DCOUNTA             | CATEGORY_DATABASE              | PHPExcel_Calculation_Database::DCOUNTA
    DDB                 | CATEGORY_FINANCIAL             | PHPExcel_Calculation_Financial::DDB
    DEC2BIN             | CATEGORY_ENGINEERING           | PHPExcel_Calculation_Engineering::DECTOBIN
    DEC2HEX             | CATEGORY_ENGINEERING           | PHPExcel_Calculation_Engineering::DECTOHEX
    DEC2OCT             | CATEGORY_ENGINEERING           | PHPExcel_Calculation_Engineering::DECTOOCT
    DEGREES             | CATEGORY_MATH_AND_TRIG         | rad2deg
    DELTA               | CATEGORY_ENGINEERING           | PHPExcel_Calculation_Engineering::DELTA
    DEVSQ               | CATEGORY_STATISTICAL           | PHPExcel_Calculation_Statistical::DEVSQ
    DGET                | CATEGORY_DATABASE              | PHPExcel_Calculation_Database::DGET
    DISC                | CATEGORY_FINANCIAL             | PHPExcel_Calculation_Financial::DISC
    DMAX                | CATEGORY_DATABASE              | PHPExcel_Calculation_Database::DMAX
    DMIN                | CATEGORY_DATABASE              | PHPExcel_Calculation_Database::DMIN
    DOLLAR              | CATEGORY_TEXT_AND_DATA         | PHPExcel_Calculation_TextData::DOLLAR
    DOLLARDE            | CATEGORY_FINANCIAL             | PHPExcel_Calculation_Financial::DOLLARDE
    DOLLARFR            | CATEGORY_FINANCIAL             | PHPExcel_Calculation_Financial::DOLLARFR
    DPRODUCT            | CATEGORY_DATABASE              | PHPExcel_Calculation_Database::DPRODUCT
    DSTDEV              | CATEGORY_DATABASE              | PHPExcel_Calculation_Database::DSTDEV
    DSTDEVP             | CATEGORY_DATABASE              | PHPExcel_Calculation_Database::DSTDEVP
    DSUM                | CATEGORY_DATABASE              | PHPExcel_Calculation_Database::DSUM
    DURATION            | CATEGORY_FINANCIAL             | **\*\*\*  Not yet Implemented**
    DVAR                | CATEGORY_DATABASE              | PHPExcel_Calculation_Database::DVAR
    DVARP               | CATEGORY_DATABASE              | PHPExcel_Calculation_Database::DVARP

#E

    Excel Function      | Category                       | PHPExcel Function
    --------------------|--------------------------------|-------------------------------------------
    EDATE               | CATEGORY_DATE_AND_TIME         | PHPExcel_Calculation_DateTime::EDATE
    EFFECT              | CATEGORY_FINANCIAL             | PHPExcel_Calculation_Financial::EFFECT
    EOMONTH             | CATEGORY_DATE_AND_TIME         | PHPExcel_Calculation_DateTime::EOMONTH
    ERF                 | CATEGORY_ENGINEERING           | PHPExcel_Calculation_Engineering::ERF
    ERFC                | CATEGORY_ENGINEERING           | PHPExcel_Calculation_Engineering::ERFC
    ERROR.TYPE          | CATEGORY_INFORMATION           | PHPExcel_Calculation_Functions::ERROR_TYPE
    EVEN                | CATEGORY_MATH_AND_TRIG         | PHPExcel_Calculation_MathTrig::EVEN
    EXACT               | CATEGORY_TEXT_AND_DATA         | **\*\*\*  Not yet Implemented**
    EXP                 | CATEGORY_MATH_AND_TRIG         | exp
    EXPONDIST           | CATEGORY_STATISTICAL           | PHPExcel_Calculation_Statistical::EXPONDIST

#F

    Excel Function      | Category                       | PHPExcel Function
    --------------------|--------------------------------|-------------------------------------------
    FACT                | CATEGORY_MATH_AND_TRIG         | PHPExcel_Calculation_MathTrig::FACT
    FACTDOUBLE          | CATEGORY_MATH_AND_TRIG         | PHPExcel_Calculation_MathTrig::FACTDOUBLE
    FALSE               | CATEGORY_LOGICAL               | PHPExcel_Calculation_Logical::FALSE
    FDIST               | CATEGORY_STATISTICAL           | **\*\*\*  Not yet Implemented**
    FIND                | CATEGORY_TEXT_AND_DATA         | PHPExcel_Calculation_TextData::SEARCHSENSITIVE
    FINDB               | CATEGORY_TEXT_AND_DATA         | PHPExcel_Calculation_TextData::SEARCHSENSITIVE
    FINV                | CATEGORY_STATISTICAL           | **\*\*\*  Not yet Implemented**
    FISHER              | CATEGORY_STATISTICAL           | PHPExcel_Calculation_Statistical::FISHER
    FISHERINV           | CATEGORY_STATISTICAL           | PHPExcel_Calculation_Statistical::FISHERINV
    FIXED               | CATEGORY_TEXT_AND_DATA         | PHPExcel_Calculation_TextData::FIXEDFORMAT
    FLOOR               | CATEGORY_MATH_AND_TRIG         | PHPExcel_Calculation_MathTrig::FLOOR
    FORECAST            | CATEGORY_STATISTICAL           | PHPExcel_Calculation_Statistical::FORECAST
    FREQUENCY           | CATEGORY_STATISTICAL           | **\*\*\*  Not yet Implemented**
    FTEST               | CATEGORY_STATISTICAL           | **\*\*\*  Not yet Implemented**
    FV                  | CATEGORY_FINANCIAL             | PHPExcel_Calculation_Financial::FV
    FVSCHEDULE          | CATEGORY_FINANCIAL             | PHPExcel_Calculation_Financial::FVSCHEDULE

#G

    Excel Function      | Category                       | PHPExcel Function
    --------------------|--------------------------------|-------------------------------------------
    GAMMADIST           | CATEGORY_STATISTICAL           | PHPExcel_Calculation_Statistical::GAMMADIST
    GAMMAINV            | CATEGORY_STATISTICAL           | PHPExcel_Calculation_Statistical::GAMMAINV
    GAMMALN             | CATEGORY_STATISTICAL           | PHPExcel_Calculation_Statistical::GAMMALN
    GCD                 | CATEGORY_MATH_AND_TRIG         | PHPExcel_Calculation_MathTrig::GCD
    GEOMEAN             | CATEGORY_STATISTICAL           | PHPExcel_Calculation_Statistical::GEOMEAN
    GESTEP              | CATEGORY_ENGINEERING           | PHPExcel_Calculation_Engineering::GESTEP
    GETPIVOTDATA        | CATEGORY_LOOKUP_AND_REFERENCE  | **\*\*\*  Not yet Implemented**
    GROWTH              | CATEGORY_STATISTICAL           | PHPExcel_Calculation_Statistical::GROWTH

#H

    Excel Function      | Category                       | PHPExcel Function
    --------------------|--------------------------------|-------------------------------------------
    HARMEAN             | CATEGORY_STATISTICAL           | PHPExcel_Calculation_Statistical::HARMEAN
    HEX2BIN             | CATEGORY_ENGINEERING           | PHPExcel_Calculation_Engineering::HEXTOBIN
    HEX2DEC             | CATEGORY_ENGINEERING           | PHPExcel_Calculation_Engineering::HEXTODEC
    HEX2OCT             | CATEGORY_ENGINEERING           | PHPExcel_Calculation_Engineering::HEXTOOCT
    HLOOKUP             | CATEGORY_LOOKUP_AND_REFERENCE  | **\*\*\*  Not yet Implemented**
    HOUR                | CATEGORY_DATE_AND_TIME         | PHPExcel_Calculation_DateTime::HOUROFDAY
    HYPERLINK           | CATEGORY_LOOKUP_AND_REFERENCE  | PHPExcel_Calculation_LookupRef::HYPERLINK
    HYPGEOMDIST         | CATEGORY_STATISTICAL           | PHPExcel_Calculation_Statistical::HYPGEOMDIST

#I

    Excel Function      | Category                       | PHPExcel Function
    --------------------|--------------------------------|-------------------------------------------
    IF                  | CATEGORY_LOGICAL               | PHPExcel_Calculation_Logical::STATEMENT_IF
    IFERROR             | CATEGORY_LOGICAL               | PHPExcel_Calculation_Logical::IFERROR
    IMABS               | CATEGORY_ENGINEERING           | PHPExcel_Calculation_Engineering::IMABS
    IMAGINARY           | CATEGORY_ENGINEERING           | PHPExcel_Calculation_Engineering::IMAGINARY
    IMARGUMENT          | CATEGORY_ENGINEERING           | PHPExcel_Calculation_Engineering::IMARGUMENT
    IMCONJUGATE         | CATEGORY_ENGINEERING           | PHPExcel_Calculation_Engineering::IMCONJUGATE
    IMCOS               | CATEGORY_ENGINEERING           | PHPExcel_Calculation_Engineering::IMCOS
    IMDIV               | CATEGORY_ENGINEERING           | PHPExcel_Calculation_Engineering::IMDIV
    IMEXP               | CATEGORY_ENGINEERING           | PHPExcel_Calculation_Engineering::IMEXP
    IMLN                | CATEGORY_ENGINEERING           | PHPExcel_Calculation_Engineering::IMLN
    IMLOG10             | CATEGORY_ENGINEERING           | PHPExcel_Calculation_Engineering::IMLOG10
    IMLOG2              | CATEGORY_ENGINEERING           | PHPExcel_Calculation_Engineering::IMLOG2
    IMPOWER             | CATEGORY_ENGINEERING           | PHPExcel_Calculation_Engineering::IMPOWER
    IMPRODUCT           | CATEGORY_ENGINEERING           | PHPExcel_Calculation_Engineering::IMPRODUCT
    IMREAL              | CATEGORY_ENGINEERING           | PHPExcel_Calculation_Engineering::IMREAL
    IMSIN               | CATEGORY_ENGINEERING           | PHPExcel_Calculation_Engineering::IMSIN
    IMSQRT              | CATEGORY_ENGINEERING           | PHPExcel_Calculation_Engineering::IMSQRT
    IMSUB               | CATEGORY_ENGINEERING           | PHPExcel_Calculation_Engineering::IMSUB
    IMSUM               | CATEGORY_ENGINEERING           | PHPExcel_Calculation_Engineering::IMSUM
    INDEX               | CATEGORY_LOOKUP_AND_REFERENCE  | PHPExcel_Calculation_LookupRef::INDEX
    INDIRECT            | CATEGORY_LOOKUP_AND_REFERENCE  | PHPExcel_Calculation_LookupRef::INDIRECT
    INFO                | CATEGORY_INFORMATION           | **\*\*\*  Not yet Implemented**
    INT                 | CATEGORY_MATH_AND_TRIG         | PHPExcel_Calculation_MathTrig::INT
    INTERCEPT           | CATEGORY_STATISTICAL           | PHPExcel_Calculation_Statistical::INTERCEPT
    INTRATE             | CATEGORY_FINANCIAL             | PHPExcel_Calculation_Financial::INTRATE
    IPMT                | CATEGORY_FINANCIAL             | PHPExcel_Calculation_Financial::IPMT
    IRR                 | CATEGORY_FINANCIAL             | PHPExcel_Calculation_Financial::IRR
    ISBLANK             | CATEGORY_INFORMATION           | PHPExcel_Calculation_Functions::IS_BLANK
    ISERR               | CATEGORY_INFORMATION           | PHPExcel_Calculation_Functions::IS_ERR
    ISERROR             | CATEGORY_INFORMATION           | PHPExcel_Calculation_Functions::IS_ERROR
    ISEVEN              | CATEGORY_INFORMATION           | PHPExcel_Calculation_Functions::IS_EVEN
    ISLOGICAL           | CATEGORY_INFORMATION           | PHPExcel_Calculation_Functions::IS_LOGICAL
    ISNA                | CATEGORY_INFORMATION           | PHPExcel_Calculation_Functions::IS_NA
    ISNONTEXT           | CATEGORY_INFORMATION           | PHPExcel_Calculation_Functions::IS_NONTEXT
    ISNUMBER            | CATEGORY_INFORMATION           | PHPExcel_Calculation_Functions::IS_NUMBER
    ISODD               | CATEGORY_INFORMATION           | PHPExcel_Calculation_Functions::IS_ODD
    ISPMT               | CATEGORY_FINANCIAL             | PHPExcel_Calculation_Financial::ISPMT
    ISREF               | CATEGORY_INFORMATION           | **\*\*\*  Not yet Implemented**
    ISTEXT              | CATEGORY_INFORMATION           | PHPExcel_Calculation_Functions::IS_TEXT

#J

    Excel Function      | Category                       | PHPExcel Function
    --------------------|--------------------------------|-------------------------------------------
    JIS                 | CATEGORY_TEXT_AND_DATA         | **\*\*\*  Not yet Implemented**

#K

    Excel Function      | Category                       | PHPExcel Function
    --------------------|--------------------------------|-------------------------------------------
    KURT                | CATEGORY_STATISTICAL           | PHPExcel_Calculation_Statistical::KURT

#L

    Excel Function      | Category                       | PHPExcel Function
    --------------------|--------------------------------|-------------------------------------------
    LARGE               | CATEGORY_STATISTICAL           | PHPExcel_Calculation_Statistical::LARGE
    LCM                 | CATEGORY_MATH_AND_TRIG         | PHPExcel_Calculation_MathTrig::LCM
    LEFT                | CATEGORY_TEXT_AND_DATA         | PHPExcel_Calculation_TextData::LEFT
    LEFTB               | CATEGORY_TEXT_AND_DATA         | PHPExcel_Calculation_TextData::LEFT
    LEN                 | CATEGORY_TEXT_AND_DATA         | PHPExcel_Calculation_TextData::STRINGLENGTH
    LENB                | CATEGORY_TEXT_AND_DATA         | PHPExcel_Calculation_TextData::STRINGLENGTH
    LINEST              | CATEGORY_STATISTICAL           | PHPExcel_Calculation_Statistical::LINEST
    LN                  | CATEGORY_MATH_AND_TRIG         | log
    LOG                 | CATEGORY_MATH_AND_TRIG         | PHPExcel_Calculation_MathTrig::LOG_BASE
    LOG10               | CATEGORY_MATH_AND_TRIG         | log10
    LOGEST              | CATEGORY_STATISTICAL           | PHPExcel_Calculation_Statistical::LOGEST
    LOGINV              | CATEGORY_STATISTICAL           | PHPExcel_Calculation_Statistical::LOGINV
    LOGNORMDIST         | CATEGORY_STATISTICAL           | PHPExcel_Calculation_Statistical::LOGNORMDIST
    LOOKUP              | CATEGORY_LOOKUP_AND_REFERENCE  | PHPExcel_Calculation_LookupRef::LOOKUP
    LOWER               | CATEGORY_TEXT_AND_DATA         | PHPExcel_Calculation_TextData::LOWERCASE

#M

    Excel Function      | Category                       | PHPExcel Function
    --------------------|--------------------------------|-------------------------------------------
    MATCH               | CATEGORY_LOOKUP_AND_REFERENCE  | PHPExcel_Calculation_LookupRef::MATCH
    MAX                 | CATEGORY_STATISTICAL           | PHPExcel_Calculation_Statistical::MAX
    MAXA                | CATEGORY_STATISTICAL           | PHPExcel_Calculation_Statistical::MAXA
    MAXIF               | CATEGORY_STATISTICAL           | PHPExcel_Calculation_Statistical::MAXIF
    MDETERM             | CATEGORY_MATH_AND_TRIG         | PHPExcel_Calculation_MathTrig::MDETERM
    MDURATION           | CATEGORY_FINANCIAL             | **\*\*\*  Not yet Implemented**
    MEDIAN              | CATEGORY_STATISTICAL           | PHPExcel_Calculation_Statistical::MEDIAN
    MEDIANIF            | CATEGORY_STATISTICAL           | **\*\*\*  Not yet Implemented**
    MID                 | CATEGORY_TEXT_AND_DATA         | PHPExcel_Calculation_TextData::MID
    MIDB                | CATEGORY_TEXT_AND_DATA         | PHPExcel_Calculation_TextData::MID
    MIN                 | CATEGORY_STATISTICAL           | PHPExcel_Calculation_Statistical::MIN
    MINA                | CATEGORY_STATISTICAL           | PHPExcel_Calculation_Statistical::MINA
    MINIF               | CATEGORY_STATISTICAL           | PHPExcel_Calculation_Statistical::MINIF
    MINUTE              | CATEGORY_DATE_AND_TIME         | PHPExcel_Calculation_DateTime::MINUTEOFHOUR
    MINVERSE            | CATEGORY_MATH_AND_TRIG         | PHPExcel_Calculation_MathTrig::MINVERSE
    MIRR                | CATEGORY_FINANCIAL             | PHPExcel_Calculation_Financial::MIRR
    MMULT               | CATEGORY_MATH_AND_TRIG         | PHPExcel_Calculation_MathTrig::MMULT
    MOD                 | CATEGORY_MATH_AND_TRIG         | PHPExcel_Calculation_MathTrig::MOD
    MODE                | CATEGORY_STATISTICAL           | PHPExcel_Calculation_Statistical::MODE
    MONTH               | CATEGORY_DATE_AND_TIME         | PHPExcel_Calculation_DateTime::MONTHOFYEAR
    MROUND              | CATEGORY_MATH_AND_TRIG         | PHPExcel_Calculation_MathTrig::MROUND
    MULTINOMIAL         | CATEGORY_MATH_AND_TRIG         | PHPExcel_Calculation_MathTrig::MULTINOMIAL

#N

    Excel Function      | Category                       | PHPExcel Function
    --------------------|--------------------------------|-------------------------------------------
    N                   | CATEGORY_INFORMATION           | PHPExcel_Calculation_Functions::N
    NA                  | CATEGORY_INFORMATION           | PHPExcel_Calculation_Functions::NA
    NEGBINOMDIST        | CATEGORY_STATISTICAL           | PHPExcel_Calculation_Statistical::NEGBINOMDIST
    NETWORKDAYS         | CATEGORY_DATE_AND_TIME         | PHPExcel_Calculation_DateTime::NETWORKDAYS
    NOMINAL             | CATEGORY_FINANCIAL             | PHPExcel_Calculation_Financial::NOMINAL
    NORMDIST            | CATEGORY_STATISTICAL           | PHPExcel_Calculation_Statistical::NORMDIST
    NORMINV             | CATEGORY_STATISTICAL           | PHPExcel_Calculation_Statistical::NORMINV
    NORMSDIST           | CATEGORY_STATISTICAL           | PHPExcel_Calculation_Statistical::NORMSDIST
    NORMSINV            | CATEGORY_STATISTICAL           | PHPExcel_Calculation_Statistical::NORMSINV
    NOT                 | CATEGORY_LOGICAL               | PHPExcel_Calculation_Logical::NOT
    NOW                 | CATEGORY_DATE_AND_TIME         | PHPExcel_Calculation_DateTime::DATETIMENOW
    NPER                | CATEGORY_FINANCIAL             | PHPExcel_Calculation_Financial::NPER
    NPV                 | CATEGORY_FINANCIAL             | PHPExcel_Calculation_Financial::NPV

#O

    Excel Function      | Category                       | PHPExcel Function
    --------------------|--------------------------------|-------------------------------------------
    OCT2BIN             | CATEGORY_ENGINEERING           | PHPExcel_Calculation_Engineering::OCTTOBIN
    OCT2DEC             | CATEGORY_ENGINEERING           | PHPExcel_Calculation_Engineering::OCTTODEC
    OCT2HEX             | CATEGORY_ENGINEERING           | PHPExcel_Calculation_Engineering::OCTTOHEX
    ODD                 | CATEGORY_MATH_AND_TRIG         | PHPExcel_Calculation_MathTrig::ODD
    ODDFPRICE           | CATEGORY_FINANCIAL             | **\*\*\*  Not yet Implemented**
    ODDFYIELD           | CATEGORY_FINANCIAL             | **\*\*\*  Not yet Implemented**
    ODDLPRICE           | CATEGORY_FINANCIAL             | **\*\*\*  Not yet Implemented**
    ODDLYIELD           | CATEGORY_FINANCIAL             | **\*\*\*  Not yet Implemented**
    OFFSET              | CATEGORY_LOOKUP_AND_REFERENCE  | PHPExcel_Calculation_LookupRef::OFFSET
    OR                  | CATEGORY_LOGICAL               | PHPExcel_Calculation_Logical::LOGICAL_OR

#P

    Excel Function      | Category                       | PHPExcel Function
    --------------------|--------------------------------|-------------------------------------------
    PEARSON             | CATEGORY_STATISTICAL           | PHPExcel_Calculation_Statistical::CORREL
    PERCENTILE          | CATEGORY_STATISTICAL           | PHPExcel_Calculation_Statistical::PERCENTILE
    PERCENTRANK         | CATEGORY_STATISTICAL           | PHPExcel_Calculation_Statistical::PERCENTRANK
    PERMUT              | CATEGORY_STATISTICAL           | PHPExcel_Calculation_Statistical::PERMUT
    PHONETIC            | CATEGORY_TEXT_AND_DATA         | **\*\*\*  Not yet Implemented**
    PI                  | CATEGORY_MATH_AND_TRIG         | pi
    PMT                 | CATEGORY_FINANCIAL             | PHPExcel_Calculation_Financial::PMT
    POISSON             | CATEGORY_STATISTICAL           | PHPExcel_Calculation_Statistical::POISSON
    POWER               | CATEGORY_MATH_AND_TRIG         | PHPExcel_Calculation_MathTrig::POWER
    PPMT                | CATEGORY_FINANCIAL             | PHPExcel_Calculation_Financial::PPMT
    PRICE               | CATEGORY_FINANCIAL             | PHPExcel_Calculation_Financial::PRICE
    PRICEDISC           | CATEGORY_FINANCIAL             | PHPExcel_Calculation_Financial::PRICEDISC
    PRICEMAT            | CATEGORY_FINANCIAL             | PHPExcel_Calculation_Financial::PRICEMAT
    PROB                | CATEGORY_STATISTICAL           | **\*\*\*  Not yet Implemented**
    PRODUCT             | CATEGORY_MATH_AND_TRIG         | PHPExcel_Calculation_MathTrig::PRODUCT
    PROPER              | CATEGORY_TEXT_AND_DATA         | PHPExcel_Calculation_TextData::PROPERCASE
    PV                  | CATEGORY_FINANCIAL             | PHPExcel_Calculation_Financial::PV

#Q

    Excel Function      | Category                       | PHPExcel Function
    --------------------|--------------------------------|-------------------------------------------
    QUARTILE            | CATEGORY_STATISTICAL           | PHPExcel_Calculation_Statistical::QUARTILE
    QUOTIENT            | CATEGORY_MATH_AND_TRIG         | PHPExcel_Calculation_MathTrig::QUOTIENT

#R

    Excel Function      | Category                       | PHPExcel Function
    --------------------|--------------------------------|-------------------------------------------
    RADIANS             | CATEGORY_MATH_AND_TRIG         | deg2rad
    RAND                | CATEGORY_MATH_AND_TRIG         | PHPExcel_Calculation_MathTrig::RAND
    RANDBETWEEN         | CATEGORY_MATH_AND_TRIG         | PHPExcel_Calculation_MathTrig::RAND
    RANK                | CATEGORY_STATISTICAL           | PHPExcel_Calculation_Statistical::RANK
    RATE                | CATEGORY_FINANCIAL             | PHPExcel_Calculation_Financial::RATE
    RECEIVED            | CATEGORY_FINANCIAL             | PHPExcel_Calculation_Financial::RECEIVED
    REPLACE             | CATEGORY_TEXT_AND_DATA         | PHPExcel_Calculation_TextData::REPLACE
    REPLACEB            | CATEGORY_TEXT_AND_DATA         | PHPExcel_Calculation_TextData::REPLACE
    REPT                | CATEGORY_TEXT_AND_DATA         | str_repeat
    RIGHT               | CATEGORY_TEXT_AND_DATA         | PHPExcel_Calculation_TextData::RIGHT
    RIGHTB              | CATEGORY_TEXT_AND_DATA         | PHPExcel_Calculation_TextData::RIGHT
    ROMAN               | CATEGORY_MATH_AND_TRIG         | PHPExcel_Calculation_MathTrig::ROMAN
    ROUND               | CATEGORY_MATH_AND_TRIG         | round
    ROUNDDOWN           | CATEGORY_MATH_AND_TRIG         | PHPExcel_Calculation_MathTrig::ROUNDDOWN
    ROUNDUP             | CATEGORY_MATH_AND_TRIG         | PHPExcel_Calculation_MathTrig::ROUNDUP
    ROW                 | CATEGORY_LOOKUP_AND_REFERENCE  | PHPExcel_Calculation_LookupRef::ROW
    ROWS                | CATEGORY_LOOKUP_AND_REFERENCE  | PHPExcel_Calculation_LookupRef::ROWS
    RSQ                 | CATEGORY_STATISTICAL           | PHPExcel_Calculation_Statistical::RSQ
    RTD                 | CATEGORY_LOOKUP_AND_REFERENCE  | **\*\*\*  Not yet Implemented**

#S

    Excel Function      | Category                       | PHPExcel Function
    --------------------|--------------------------------|-------------------------------------------
    SEARCH              | CATEGORY_TEXT_AND_DATA         | PHPExcel_Calculation_TextData::SEARCHINSENSITIVE
    SEARCHB             | CATEGORY_TEXT_AND_DATA         | PHPExcel_Calculation_TextData::SEARCHINSENSITIVE
    SECOND              | CATEGORY_DATE_AND_TIME         | PHPExcel_Calculation_DateTime::SECONDOFMINUTE
    SERIESSUM           | CATEGORY_MATH_AND_TRIG         | PHPExcel_Calculation_MathTrig::SERIESSUM
    SIGN                | CATEGORY_MATH_AND_TRIG         | PHPExcel_Calculation_MathTrig::SIGN
    SIN                 | CATEGORY_MATH_AND_TRIG         | sin
    SINH                | CATEGORY_MATH_AND_TRIG         | sinh
    SKEW                | CATEGORY_STATISTICAL           | PHPExcel_Calculation_Statistical::SKEW
    SLN                 | CATEGORY_FINANCIAL             | PHPExcel_Calculation_Financial::SLN
    SLOPE               | CATEGORY_STATISTICAL           | PHPExcel_Calculation_Statistical::SLOPE
    SMALL               | CATEGORY_STATISTICAL           | PHPExcel_Calculation_Statistical::SMALL
    SQRT                | CATEGORY_MATH_AND_TRIG         | sqrt
    SQRTPI              | CATEGORY_MATH_AND_TRIG         | PHPExcel_Calculation_MathTrig::SQRTPI
    STANDARDIZE         | CATEGORY_STATISTICAL           | PHPExcel_Calculation_Statistical::STANDARDIZE
    STDEV               | CATEGORY_STATISTICAL           | PHPExcel_Calculation_Statistical::STDEV
    STDEVA              | CATEGORY_STATISTICAL           | PHPExcel_Calculation_Statistical::STDEVA
    STDEVP              | CATEGORY_STATISTICAL           | PHPExcel_Calculation_Statistical::STDEVP
    STDEVPA             | CATEGORY_STATISTICAL           | PHPExcel_Calculation_Statistical::STDEVPA
    STEYX               | CATEGORY_STATISTICAL           | PHPExcel_Calculation_Statistical::STEYX
    SUBSTITUTE          | CATEGORY_TEXT_AND_DATA         | PHPExcel_Calculation_TextData::SUBSTITUTE
    SUBTOTAL            | CATEGORY_MATH_AND_TRIG         | PHPExcel_Calculation_MathTrig::SUBTOTAL
    SUM                 | CATEGORY_MATH_AND_TRIG         | PHPExcel_Calculation_MathTrig::SUM
    SUMIF               | CATEGORY_MATH_AND_TRIG         | PHPExcel_Calculation_MathTrig::SUMIF
    SUMIFS              | CATEGORY_MATH_AND_TRIG         | **\*\*\*  Not yet Implemented**
    SUMPRODUCT          | CATEGORY_MATH_AND_TRIG         | PHPExcel_Calculation_MathTrig::SUMPRODUCT
    SUMSQ               | CATEGORY_MATH_AND_TRIG         | PHPExcel_Calculation_MathTrig::SUMSQ
    SUMX2MY2            | CATEGORY_MATH_AND_TRIG         | PHPExcel_Calculation_MathTrig::SUMX2MY2
    SUMX2PY2            | CATEGORY_MATH_AND_TRIG         | PHPExcel_Calculation_MathTrig::SUMX2PY2
    SUMXMY2             | CATEGORY_MATH_AND_TRIG         | PHPExcel_Calculation_MathTrig::SUMXMY2
    SYD                 | CATEGORY_FINANCIAL             | PHPExcel_Calculation_Financial::SYD

#T

    Excel Function      | Category                       | PHPExcel Function
    --------------------|--------------------------------|-------------------------------------------
    T                   | CATEGORY_TEXT_AND_DATA         | PHPExcel_Calculation_TextData::RETURNSTRING
    TAN                 | CATEGORY_MATH_AND_TRIG         | tan
    TANH                | CATEGORY_MATH_AND_TRIG         | tanh
    TBILLEQ             | CATEGORY_FINANCIAL             | PHPExcel_Calculation_Financial::TBILLEQ
    TBILLPRICE          | CATEGORY_FINANCIAL             | PHPExcel_Calculation_Financial::TBILLPRICE
    TBILLYIELD          | CATEGORY_FINANCIAL             | PHPExcel_Calculation_Financial::TBILLYIELD
    TDIST               | CATEGORY_STATISTICAL           | PHPExcel_Calculation_Statistical::TDIST
    TEXT                | CATEGORY_TEXT_AND_DATA         | PHPExcel_Calculation_TextData::TEXTFORMAT
    TIME                | CATEGORY_DATE_AND_TIME         | PHPExcel_Calculation_DateTime::TIME
    TIMEVALUE           | CATEGORY_DATE_AND_TIME         | PHPExcel_Calculation_DateTime::TIMEVALUE
    TINV                | CATEGORY_STATISTICAL           | PHPExcel_Calculation_Statistical::TINV
    TODAY               | CATEGORY_DATE_AND_TIME         | PHPExcel_Calculation_DateTime::DATENOW
    TRANSPOSE           | CATEGORY_LOOKUP_AND_REFERENCE  | PHPExcel_Calculation_LookupRef::TRANSPOSE
    TREND               | CATEGORY_STATISTICAL           | PHPExcel_Calculation_Statistical::TREND
    TRIM                | CATEGORY_TEXT_AND_DATA         | PHPExcel_Calculation_TextData::TRIMSPACES
    TRIMMEAN            | CATEGORY_STATISTICAL           | PHPExcel_Calculation_Statistical::TRIMMEAN
    TRUE                | CATEGORY_LOGICAL               | PHPExcel_Calculation_Logical::TRUE
    TRUNC               | CATEGORY_MATH_AND_TRIG         | PHPExcel_Calculation_MathTrig::TRUNC
    TTEST               | CATEGORY_STATISTICAL           | **\*\*\*  Not yet Implemented**
    TYPE                | CATEGORY_INFORMATION           | PHPExcel_Calculation_Functions::TYPE

#U

    Excel Function      | Category                       | PHPExcel Function
    --------------------|--------------------------------|-------------------------------------------
    UPPER               | CATEGORY_TEXT_AND_DATA         | PHPExcel_Calculation_TextData::UPPERCASE
    USDOLLAR            | CATEGORY_FINANCIAL             | **\*\*\*  Not yet Implemented**

#V

    Excel Function      | Category                       | PHPExcel Function
    --------------------|--------------------------------|-------------------------------------------
    VALUE               | CATEGORY_TEXT_AND_DATA         | **\*\*\*  Not yet Implemented**
    VAR                 | CATEGORY_STATISTICAL           | PHPExcel_Calculation_Statistical::VARFunc
    VARA                | CATEGORY_STATISTICAL           | PHPExcel_Calculation_Statistical::VARA
    VARP                | CATEGORY_STATISTICAL           | PHPExcel_Calculation_Statistical::VARP
    VARPA               | CATEGORY_STATISTICAL           | PHPExcel_Calculation_Statistical::VARPA
    VDB                 | CATEGORY_FINANCIAL             | **\*\*\*  Not yet Implemented**
    VERSION             | CATEGORY_INFORMATION           | PHPExcel_Calculation_Functions::VERSION
    VLOOKUP             | CATEGORY_LOOKUP_AND_REFERENCE  | PHPExcel_Calculation_LookupRef::VLOOKUP

#W

    Excel Function      | Category                       | PHPExcel Function
    --------------------|--------------------------------|-------------------------------------------
    WEEKDAY             | CATEGORY_DATE_AND_TIME         | PHPExcel_Calculation_DateTime::DAYOFWEEK
    WEEKNUM             | CATEGORY_DATE_AND_TIME         | PHPExcel_Calculation_DateTime::WEEKOFYEAR
    WEIBULL             | CATEGORY_STATISTICAL           | PHPExcel_Calculation_Statistical::WEIBULL
    WORKDAY             | CATEGORY_DATE_AND_TIME         | PHPExcel_Calculation_DateTime::WORKDAY

#X

    Excel Function      | Category                       | PHPExcel Function
    --------------------|--------------------------------|-------------------------------------------
    XIRR                | CATEGORY_FINANCIAL             | PHPExcel_Calculation_Financial::XIRR
    XNPV                | CATEGORY_FINANCIAL             | PHPExcel_Calculation_Financial::XNPV

#Y

    Excel Function      | Category                       | PHPExcel Function
    --------------------|--------------------------------|-------------------------------------------
    YEAR                | CATEGORY_DATE_AND_TIME         | PHPExcel_Calculation_DateTime::YEAR
    YEARFRAC            | CATEGORY_DATE_AND_TIME         | PHPExcel_Calculation_DateTime::YEARFRAC
    YIELD               | CATEGORY_FINANCIAL             | **\*\*\*  Not yet Implemented**
    YIELDDISC           | CATEGORY_FINANCIAL             | PHPExcel_Calculation_Financial::YIELDDISC
    YIELDMAT            | CATEGORY_FINANCIAL             | PHPExcel_Calculation_Financial::YIELDMAT

#Z

    Excel Function      | Category                       | PHPExcel Function
    --------------------|--------------------------------|-------------------------------------------
    ZTEST               | CATEGORY_STATISTICAL           | PHPExcel_Calculation_Statistical::ZTEST
