<?php return array(
    'root' => array(
        'pretty_version' => 'dev-master',
        'version' => 'dev-master',
        'type' => 'project',
        'install_path' => __DIR__ . '/../../',
        'aliases' => array(),
        'reference' => '47a5cb7450bb583fd2a88ec4c403a2274156c5f4',
        'name' => 'topthink/think',
        'dev' => true,
    ),
    'versions' => array(
        '5ini99/think-auth' => array(
            'pretty_version' => '1.0.1',
            'version' => '1.0.1.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../5ini99/think-auth',
            'aliases' => array(),
            'reference' => 'f9f20d18b5e400d67312a7ca75e4dc35be630a4c',
            'dev_requirement' => false,
        ),
        'leruge/auth' => array(
            'pretty_version' => '1.6',
            'version' => '1.6.0.0',
            'type' => 'think-extend',
            'install_path' => __DIR__ . '/../leruge/auth',
            'aliases' => array(),
            'reference' => '1795bc06625c55d6098740d6dff027bdaae69c6f',
            'dev_requirement' => false,
        ),
        'phpoffice/phpexcel' => array(
            'pretty_version' => '1.8.2',
            'version' => '1.8.2.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../phpoffice/phpexcel',
            'aliases' => array(),
            'reference' => '1441011fb7ecdd8cc689878f54f8b58a6805f870',
            'dev_requirement' => false,
        ),
        'topthink/framework' => array(
            'pretty_version' => 'v5.1.41',
            'version' => '5.1.41.0',
            'type' => 'think-framework',
            'install_path' => __DIR__ . '/../../thinkphp',
            'aliases' => array(),
            'reference' => '7137741a323a4a60cfca334507cd1812fac91bb2',
            'dev_requirement' => false,
        ),
        'topthink/think' => array(
            'pretty_version' => 'dev-master',
            'version' => 'dev-master',
            'type' => 'project',
            'install_path' => __DIR__ . '/../../',
            'aliases' => array(),
            'reference' => '47a5cb7450bb583fd2a88ec4c403a2274156c5f4',
            'dev_requirement' => false,
        ),
        'topthink/think-captcha' => array(
            'pretty_version' => 'v2.0.2',
            'version' => '2.0.2.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../topthink/think-captcha',
            'aliases' => array(),
            'reference' => '54c8a51552f99ff9ea89ea9c272383a8f738ceee',
            'dev_requirement' => false,
        ),
        'topthink/think-helper' => array(
            'pretty_version' => 'v1.0.7',
            'version' => '1.0.7.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../topthink/think-helper',
            'aliases' => array(),
            'reference' => '5f92178606c8ce131d36b37a57c58eb71e55f019',
            'dev_requirement' => false,
        ),
        'topthink/think-image' => array(
            'pretty_version' => 'v1.0.7',
            'version' => '1.0.7.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../topthink/think-image',
            'aliases' => array(),
            'reference' => '8586cf47f117481c6d415b20f7dedf62e79d5512',
            'dev_requirement' => false,
        ),
        'topthink/think-installer' => array(
            'pretty_version' => 'v2.0.5',
            'version' => '2.0.5.0',
            'type' => 'composer-plugin',
            'install_path' => __DIR__ . '/../topthink/think-installer',
            'aliases' => array(),
            'reference' => '38ba647706e35d6704b5d370c06f8a160b635f88',
            'dev_requirement' => false,
        ),
    ),
);
