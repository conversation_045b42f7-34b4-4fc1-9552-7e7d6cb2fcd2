{"packages": [{"name": "5ini99/think-auth", "version": "1.0.1", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/5ini99/think-auth.git", "reference": "f9f20d18b5e400d67312a7ca75e4dc35be630a4c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/5ini99/think-auth/zipball/f9f20d18b5e400d67312a7ca75e4dc35be630a4c", "reference": "f9f20d18b5e400d67312a7ca75e4dc35be630a4c", "shasum": ""}, "require": {"php": ">=5.4.0"}, "time": "2016-11-02T06:26:09+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"think\\auth\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "xiaobo.sun", "email": "<EMAIL>"}], "description": "auth package for thinkphp5", "homepage": "https://github.com/5ini99/think-auth", "support": {"issues": "https://github.com/5ini99/think-auth/issues", "source": "https://github.com/5ini99/think-auth/tree/master"}, "install-path": "../5ini99/think-auth"}, {"name": "leruge/auth", "version": "1.6", "version_normalized": "*******", "source": {"type": "git", "url": "https://gitee.com/sjclub/auth", "reference": "1795bc06625c55d6098740d6dff027bdaae69c6f"}, "require": {"php": ">=5.6.0", "topthink/framework": "5.1.*"}, "time": "2020-06-15T01:05:15+00:00", "type": "think-extend", "extra": {"think-config": {"auth": "src/config.php"}}, "installation-source": "source", "autoload": {"psr-4": {"leruge\\auth\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "leruge", "email": "<EMAIL>"}], "description": "tp5.1权限认证类", "install-path": "../leruge/auth"}, {"name": "phpoffice/phpexcel", "version": "1.8.2", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/PHPOffice/PHPExcel.git", "reference": "1441011fb7ecdd8cc689878f54f8b58a6805f870"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/PHPOffice/PHPExcel/zipball/1441011fb7ecdd8cc689878f54f8b58a6805f870", "reference": "1441011fb7ecdd8cc689878f54f8b58a6805f870", "shasum": ""}, "require": {"ext-mbstring": "*", "ext-xml": "*", "ext-xmlwriter": "*", "php": "^5.2|^7.0"}, "require-dev": {"squizlabs/php_codesniffer": "2.*"}, "time": "2018-11-22T23:07:24+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-0": {"PHPExcel": "Classes/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["LGPL-2.1"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "homepage": "http://blog.maartenballiauw.be"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON>", "homepage": "http://rootslabs.net"}, {"name": "<PERSON>", "homepage": "http://markbakeruk.net"}], "description": "PHPExcel - OpenXML - Read, Create and Write Spreadsheet documents in PHP - Spreadsheet engine", "homepage": "https://github.com/PHPOffice/PHPExcel", "keywords": ["OpenXML", "excel", "php", "spreadsheet", "xls", "xlsx"], "support": {"issues": "https://github.com/PHPOffice/PHPExcel/issues", "source": "https://github.com/PHPOffice/PHPExcel/tree/master"}, "abandoned": "phpoffice/phpspreadsheet", "install-path": "../phpoffice/phpexcel"}, {"name": "topthink/framework", "version": "v5.1.41", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/top-think/framework.git", "reference": "7137741a323a4a60cfca334507cd1812fac91bb2"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/top-think/framework/zipball/7137741a323a4a60cfca334507cd1812fac91bb2", "reference": "7137741a323a4a60cfca334507cd1812fac91bb2", "shasum": ""}, "require": {"php": ">=5.6.0", "topthink/think-installer": "2.*"}, "require-dev": {"johnkary/phpunit-speedtrap": "^1.0", "mikey179/vfsstream": "~1.6", "phpdocumentor/reflection-docblock": "^2.0", "phploc/phploc": "2.*", "phpunit/phpunit": "^5.0|^6.0", "sebastian/phpcpd": "2.*", "squizlabs/php_codesniffer": "2.*"}, "time": "2021-01-11T02:51:29+00:00", "type": "think-framework", "installation-source": "dist", "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "liu21st", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "the new thinkphp framework", "homepage": "http://thinkphp.cn/", "keywords": ["framework", "orm", "thinkphp"], "install-path": "../../thinkphp"}, {"name": "topthink/think-captcha", "version": "v2.0.2", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/top-think/think-captcha.git", "reference": "54c8a51552f99ff9ea89ea9c272383a8f738ceee"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/top-think/think-captcha/zipball/54c8a51552f99ff9ea89ea9c272383a8f738ceee", "reference": "54c8a51552f99ff9ea89ea9c272383a8f738ceee", "shasum": ""}, "require": {"topthink/framework": "5.1.*"}, "time": "2017-12-31T16:37:49+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"think\\captcha\\": "src/"}, "files": ["src/helper.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "captcha package for thinkphp5", "support": {"issues": "https://github.com/top-think/think-captcha/issues", "source": "https://github.com/top-think/think-captcha/tree/2.0"}, "install-path": "../topthink/think-captcha"}, {"name": "topthink/think-helper", "version": "v1.0.7", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/top-think/think-helper.git", "reference": "5f92178606c8ce131d36b37a57c58eb71e55f019"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/top-think/think-helper/zipball/5f92178606c8ce131d36b37a57c58eb71e55f019", "reference": "5f92178606c8ce131d36b37a57c58eb71e55f019", "shasum": ""}, "time": "2018-10-05T00:43:21+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"think\\helper\\": "src"}, "files": ["src/helper.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "The ThinkPHP5 Helper Package", "support": {"issues": "https://github.com/top-think/think-helper/issues", "source": "https://github.com/top-think/think-helper/tree/master"}, "install-path": "../topthink/think-helper"}, {"name": "topthink/think-image", "version": "v1.0.7", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/top-think/think-image.git", "reference": "8586cf47f117481c6d415b20f7dedf62e79d5512"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/top-think/think-image/zipball/8586cf47f117481c6d415b20f7dedf62e79d5512", "reference": "8586cf47f117481c6d415b20f7dedf62e79d5512", "shasum": ""}, "require": {"ext-gd": "*"}, "require-dev": {"phpunit/phpunit": "4.8.*", "topthink/framework": "^5.0"}, "time": "2016-09-29T06:05:43+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"think\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "The ThinkPHP5 Image Package", "support": {"issues": "https://github.com/top-think/think-image/issues", "source": "https://github.com/top-think/think-image/tree/master"}, "install-path": "../topthink/think-image"}, {"name": "topthink/think-installer", "version": "v2.0.5", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/top-think/think-installer.git", "reference": "38ba647706e35d6704b5d370c06f8a160b635f88"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/top-think/think-installer/zipball/38ba647706e35d6704b5d370c06f8a160b635f88", "reference": "38ba647706e35d6704b5d370c06f8a160b635f88", "shasum": ""}, "require": {"composer-plugin-api": "^1.0||^2.0"}, "require-dev": {"composer/composer": "^1.0||^2.0"}, "time": "2021-01-14T12:12:14+00:00", "type": "composer-plugin", "extra": {"class": "think\\composer\\Plugin"}, "installation-source": "dist", "autoload": {"psr-4": {"think\\composer\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "install-path": "../topthink/think-installer"}], "dev": true, "dev-package-names": []}