<?php

// autoload_static.php @generated by Composer

namespace Composer\Autoload;

class ComposerStaticInitcfe2878367c71be022212d538757782d
{
    public static $files = array (
        '1cfd2761b63b0a29ed23657ea394cb2d' => __DIR__ . '/..' . '/topthink/think-captcha/src/helper.php',
        '9b552a3cc426e3287cc811caefa3cf53' => __DIR__ . '/..' . '/topthink/think-helper/src/helper.php',
    );

    public static $prefixLengthsPsr4 = array (
        't' => 
        array (
            'think\\helper\\' => 13,
            'think\\composer\\' => 15,
            'think\\captcha\\' => 14,
            'think\\auth\\' => 11,
            'think\\' => 6,
        ),
        'l' => 
        array (
            'leruge\\auth\\' => 12,
        ),
        'a' => 
        array (
            'app\\' => 4,
        ),
    );

    public static $prefixDirsPsr4 = array (
        'think\\helper\\' => 
        array (
            0 => __DIR__ . '/..' . '/topthink/think-helper/src',
        ),
        'think\\composer\\' => 
        array (
            0 => __DIR__ . '/..' . '/topthink/think-installer/src',
        ),
        'think\\captcha\\' => 
        array (
            0 => __DIR__ . '/..' . '/topthink/think-captcha/src',
        ),
        'think\\auth\\' => 
        array (
            0 => __DIR__ . '/..' . '/5ini99/think-auth/src',
        ),
        'think\\' => 
        array (
            0 => __DIR__ . '/..' . '/topthink/think-image/src',
        ),
        'leruge\\auth\\' => 
        array (
            0 => __DIR__ . '/..' . '/leruge/auth/src',
        ),
        'app\\' => 
        array (
            0 => __DIR__ . '/../..' . '/application',
        ),
    );

    public static $prefixesPsr0 = array (
        'P' => 
        array (
            'PHPExcel' => 
            array (
                0 => __DIR__ . '/..' . '/phpoffice/phpexcel/Classes',
            ),
        ),
    );

    public static $classMap = array (
        'Composer\\InstalledVersions' => __DIR__ . '/..' . '/composer/InstalledVersions.php',
    );

    public static function getInitializer(ClassLoader $loader)
    {
        return \Closure::bind(function () use ($loader) {
            $loader->prefixLengthsPsr4 = ComposerStaticInitcfe2878367c71be022212d538757782d::$prefixLengthsPsr4;
            $loader->prefixDirsPsr4 = ComposerStaticInitcfe2878367c71be022212d538757782d::$prefixDirsPsr4;
            $loader->prefixesPsr0 = ComposerStaticInitcfe2878367c71be022212d538757782d::$prefixesPsr0;
            $loader->classMap = ComposerStaticInitcfe2878367c71be022212d538757782d::$classMap;

        }, null, ClassLoader::class);
    }
}
