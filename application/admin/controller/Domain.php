<?php
/**
 * @file            Domain.php
 * @version         1.0
 * @date            Thu, 22 Mar 2018 14:05:42 GMT
 * @description     域名管理
 */

namespace app\admin\controller;

use think\Controller;
use think\Db;
use think\facade\Request;


class Domain extends Base{

    public function index(){
        
        $size = request()->param('size');
        $filter['status'] = input('status', '');
        $filter['keyword'] = input('keyword', '');

        $where = [];

        if (!empty($filter['status'])) {
            $where[] = ['t1.status', '=', $filter['status']];
        }

        if (!empty($filter['keyword'])) {
            $keyword = $filter['keyword'];
            $where[] = ['t1.name|t1.consignee', 'like', "%$keyword%"];
        }
        //每页显示几条数据
        $pagesize = $size ? $size : 50;
        $rows = db('domain')->alias('t1')
            ->leftJoin("org t2","t1.org_id = t2.org_id")
            ->where($where)
            ->field('t1.*, t2.name org_name')
            ->order('t1.id desc')
            ->paginate($pagesize);
        // echo Db::getlastsql();exit;

        // 获取分页显示
        $page = $rows->render();

        $this->assign('rows', $rows);
        $this->assign('page', $page);
        $this->assign('filter', $filter);
        return $this->fetch();
    }

    public function info(){
        $params = input();

        $create = true;
        $data = array();
        $time = time();
        //新建还是编辑
        if (isset($params['id']) && !empty($params['id'])) {
            $create = false;

            $row = db('domain')->where('id', $params['id'])->find();
            if (empty($row)) {
                $this->error('信息有误，请重新操作！', url('index'));
            }
            $this->assign('row', $row);
            // $this->assign('list_agency', explode(',', $row['agency_id']));
            $this->assign('list_agency', getAgencyList($row['agency_id'], 9));
            $this->assign('list_agency_id', explode(',', $row['agency_id']));

            $data['id'] = $params['id'];
            $data['update_time'] = $time;
        } else {
            $data['create_time'] = $time;
            $data['update_time'] = $time;
        }

        if (Request::isPost()) {
            $data['org_id'] = input('org_id', 0);
            $data['url'] = trim(input('url', ''));
            $data['company'] = trim(input('company', ''));
            $data['url'] = trim(input('url', ''));
            $data['ssl_date'] = input('ssl_date', '');
            $data['remark'] = input('remark', '');
            $data['status'] = input('status', 1);
            $data['sort'] = input('sort', 0);
            if(!empty($params['agency_id'])){
                $data['agency_id'] = implode(',', $params['agency_id']);
            }

            if ($create) {
                if (!db('domain')->insertGetId($data)) {
                    $this->error('用户添加失败');
                }
            } else {
                $map = [
                    ['url', '=', $data['url']],
                    ['id', "<>", $params['id']]
                ];
                $exist = db('domain')->where($map)->count();
                if ($exist > 0) {
                    $this->error('域名已存在');
                }
                if (db('domain')->update($data) === false) {
                    $this->error('域名更新失败');
                }
            }

            $this->success("保存成功", url('index'));
        } else {
            return $this->fetch();
        }
    }
}

?>