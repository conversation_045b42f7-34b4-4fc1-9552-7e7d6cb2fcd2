<?php

/**
 * @file            AdminController.class.php
 * @version         1.0
 * @date            Fri, 23 Feb 2018 11:12:07 GMT
 * @description     This is the controller class for data "admin"
 */

namespace app\admin\controller;

use think\Controller;
use think\Db;
use think\facade\Request;

class Menu extends Base
{
    /**
     * 菜单列表
     */
    public function index()
    {
        $authRuleModel = model('AuthRule');
        $authRuleAll = $authRuleModel->getAuthRuleForPid();
        $this->assign('authRuleAll', $authRuleAll);
        return $this->fetch();
    }

    /**
     * 菜单详情
     */
    public function info()
    {
        $params = input();

        $create = true;
        $data = array();
        //新建还是编辑
        if(isset($params['id']) && !empty($params['id'])){
            $create = false;

            $authRuleRow = db('auth_rule')->where('id',$params['id'])->find();
            if (empty($authRuleRow)) {
                $this->error('信息有误，请重新操作！',url('index'));
            }

            //下拉菜单
            $parentRow = db('auth_rule')->field('id,title,pid')->where('level',1)->select();

            if($authRuleRow['level'] == 2){
                $authRuleRow['group_id'] = $authRuleRow['pid'];
            }elseif($authRuleRow['level'] == 3){
                $pid = db('auth_rule')->where("id = '".$authRuleRow['pid']."'")->value("pid");
                $authRuleRow['group_id'] = $pid;
                $authRuleRow['class_id'] = $authRuleRow['pid'];
                $classes = db('auth_rule')->field('id,title,pid')->where("pid = '$pid' AND level = 2")->select();
            }
            $this->assign('classes', $classes);
            $this->assign('parentRow', $parentRow);
            $this->assign('authRuleRow', $authRuleRow);

            $data['id'] = $params['id'];
        }

        if (Request::isPost()) {
            $data['name'] = input('name','');
            $data['title'] = input('title','');
            $data['icon'] = input('icon','');
            $data['islink'] = input('islink',0);
            $data['sort'] = input('sort',99);

            $group_id = $params['group_id'];
            $class_id = $params['class_id'];
            if($group_id > 0 && $class_id > 0){
                $data['pid'] = $class_id;
                $data['level'] = 3;
            }elseif($group_id > 0 && $class_id == 0){
                $data['pid'] = $group_id;
                $data['level'] = 2;
            }elseif($group_id == 0){
                $data['pid'] = $group_id;
                $data['level'] = 1;
            }
            
            if($create){
                $name_exist = db('auth_rule')->where("name = '".$data['name']."' and id != '".$params['id']."'")->count();
                if($name_exist > 0){
                    $this->error("菜单URL已存在");
                }
                if(!db('auth_rule')->insert($data)){
                    $this->error("菜单添加失败");
                }
            }else{
                $name_exist = db('auth_rule')->where("name = '".$data['name']."' and id != '".$params['id']."'")->count();
                if($name_exist > 0){
                    $this->error("菜单URL已存在");
                }
                if(db('auth_rule')->update($data) === false){
                    $this->error("菜单添加失败");
                }
            }

            $this->success("保存成功",url('index'));
        }else{
            $parentRow = db('auth_rule')->where("pid = 0")->order("sort ASC")->select();
            $this->assign('parentRow', $parentRow);

            return $this->fetch();
        }
    }
}
