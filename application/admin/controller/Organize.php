<?php
/**
 * @file            Organize.php
 * @version         1.0
 * @date            Thu, 22 Mar 2018 14:05:42 GMT
 * @description     经销商管理
 */

namespace app\admin\controller;

use think\Controller;
use think\Db;
use think\facade\Request;


class Organize extends Base{

    public function index(){
        
        $size = request()->param('size');
        $filter['status'] = input('status', '');
        $filter['keyword'] = input('keyword', '');

        $where = [];

        if (!empty($filter['status'])) {
            $where[] = ['t1.status', '=', $filter['status']];
        }

        if (!empty($filter['keyword'])) {
            $keyword = $filter['keyword'];
            $where[] = ['t1.name|t1.consignee', 'like', "%$keyword%"];
        }
        //每页显示几条数据
        $pagesize = $size ? $size : 50;
        $rows = db('org')->alias('t1')
            ->where($where)
            ->order('t1.org_id desc')
            ->paginate($pagesize);
        // echo Db::getlastsql();exit;

        // 获取分页显示
        $page = $rows->render();

        $this->assign('rows', $rows);
        $this->assign('page', $page);
        $this->assign('filter', $filter);
        return $this->fetch();
    }

    public function info(){
        $params = input();

        $create = true;
        $data = array();
        $time = time();
        //新建还是编辑
        if (isset($params['id']) && !empty($params['id'])) {
            $create = false;

            $row = db('org')->where('org_id', $params['id'])->find();
            if (empty($row)) {
                $this->error('信息有误，请重新操作！', url('index'));
            }
            $this->assign('row', $row);

            $data['org_id'] = $params['id'];
            $data['update_time'] = $time;
        } else {
            $data['create_time'] = $time;
            $data['update_time'] = $time;
        }

        if (Request::isPost()) {
            $data['name'] = input('name', '');
            $data['address'] = input('address', '');
            $data['consignee'] = input('consignee', '');
            $data['consignee_tel'] = input('consignee_tel', '');
            $data['remark'] = input('remark', '');
            $data['status'] = input('status', 1);
            $data['aliyun_endpoint'] = trim(input('aliyun_endpoint', ''));
            $data['aliyun_storage'] = trim(input('aliyun_storage', ''));
            $data['cdn_url'] = trim(input('cdn_url', ''));

            if ($create) {
                if (!db('org')->insertGetId($data)) {
                    $this->error('用户添加失败');
                }
            } else {
                $map = [
                    ['name', '=', $data['name']],
                    ['org_id', "<>", $params['id']]
                ];
                $exist = db('org')->where($map)->count();
                if ($exist > 0) {
                    $this->error('商户名称已存在');
                }
                if (db('org')->update($data) === false) {
                    $this->error('商户更新失败');
                }
            }

            $this->success("保存成功", url('index'));
        } else {
            return $this->fetch();
        }
    }

    public function detail(){
        $params = input();
    	if(!empty($params['vendor_id'])){
    	    $vendor_id = $params['vendor_id'];
    	    $vendor = db('Vendor')->where("vendor_id =" .$vendor_id)->find($vendor_id);
    	    if(!empty($vendor)){
    	        $this->assign('vendor', $vendor);
    	        $this->assign('back_url', get_back_url());
    	        $this->display();
    	    }else{
    	        $this->error('没找到信息');
    	    }
    	}else{
    	    $this->error('参数错误');
    	}
        return $this->fetch();
    }
}

?>