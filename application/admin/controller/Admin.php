<?php

/**
 * @file            AdminController.class.php
 * @version         1.0
 * @date            Fri, 23 Feb 2018 11:12:07 GMT
 * @description     This is the controller class for data "admin"
 */

namespace app\admin\controller;

use think\Controller;
use think\Db;
use think\facade\Request;

class Admin extends Base
{
    /**
     * 用户列表
     */
    public function index()
    {
        $size = request()->param('size');
        $filter['role_id'] = input('role_id', 0);
        $filter['status'] = input('status', 1);
        $filter['keyword'] = input('keyword', '');

        $where = [];
        if (!empty($filter['role_id'])) {
            $where[] = ['t1.role_id', '=', $filter['role_id']];
        }

        $where[] = ['t1.status', '=', $filter['status']];

        if (!empty($filter['keyword'])) {
            $keyword = $filter['keyword'];
            $where[] = ['t1.email|t1.name', 'like', "%$keyword%"];
        }
        //每页显示几条数据
        $pagesize = $size ? $size : 1;
        $adminRows = db('admin')->alias('t1')->field('t1.*,t2.name as role_name')
            ->join(config('database.prefix') . 'role t2', 't1.role_id = t2.id', 'left')
            ->where($where)
            ->order('t1.id desc')
            ->paginate($pagesize);

        // 获取分页显示
        $page = $adminRows->render();

        $this->assign('adminRows', $adminRows);
        $this->assign('page', $page);
        $this->assign('filter', $filter);
        return $this->fetch();
    }

    /**
     * 用户详情
     */
    public function info()
    {
        $params = input();

        $create = true;
        $data = array();
        $time = time();
        //新建还是编辑
        if (isset($params['id']) && !empty($params['id'])) {
            $create = false;

            $adminRow = db('admin')->where('id', $params['id'])->find();
            if (empty($adminRow)) {
                $this->error('信息有误，请重新操作！', url('index'));
            }
            $this->assign('adminRowForEdit', $adminRow);

            $data['id'] = $params['id'];
            $data['update_time'] = $time;
        } else {
            $data['create_time'] = $time;
            $data['update_time'] = $time;
        }

        if (Request::isPost()) {
            if (!empty($params['password'])) {
                if ($params['password'] != $params['password_confirm']) {
                    $this->error('密码不一致，请重新操作！', '/Admin/info');
                }
                $data['password'] = sha1(md5($params['password']));
            }
            $data['email'] = input('email', '');
            $data['name'] = input('name', '');
            $data['role_id'] = input('role_id', 0);
            $data['status'] = input('status', 1);
            $data['mobile'] = input('mobile', '');

            if ($create) {
                if (!db('admin')->insertGetId($data)) {
                    $this->error('用户添加失败');
                }
            } else {
                $map = [
                    ['email', '=', $data['email']],
                    ['id', "<>", $params['id']]
                ];
                $email_exist = db('admin')->where($map)->count();
                if ($email_exist > 0) {
                    $this->error('用户帐号已存在');
                }
                if (db('admin')->update($data) === false) {
                    $this->error('用户更新失败');
                }
            }

            $this->success("保存成功", url('index'));
        } else {
            return $this->fetch();
        }
    }

    /**
     * 账号禁用启用
     * @return [type] [description]
     */
    public function destroy()
    {
        $params = input();

        if (isset($params['id']) && !empty($params['id'])) {
            $map[] = ['id', '=', $params['id']];
            $result = db('admin')->where($map)->setField('status', $params['status']);
            if ($result !== false) {
                $this->success("保存成功", url('index'));
            }
        } else {
            $this->error('信息有误，请重新操作！', url('index'));
        }
    }
}
