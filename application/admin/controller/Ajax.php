<?php

/**
 * @file            AjaxController.class.php
 * @version         1.0
 * @date            Fri, 23 Feb 2018 11:12:07 GMT
 * @description     This is the controller class for data "ajax"
 */

namespace app\admin\controller;

use think\Controller;
use think\Model;
use think\Db;
use think\facade\Request;


class Ajax extends Controller
{
    // 获取二级菜单
    public function get_child_menus(){
        $authRuleModel = db('AuthRule');
        $pid = input('post.group_id');

        $ajax_data = $authRuleModel->field('id, title')->where("pid = $pid")->order("sort")->select();

        return json($ajax_data);
    }

    // 获取二级菜单
    public function get_child_menus_org(){
        $authRuleModel = db('OrgAuthRule');
        $pid = input('post.group_id');

        $ajax_data = $authRuleModel->field('id, title')->where("pid = $pid")->order("sort")->select();

        return json($ajax_data);
    }

    /**
     * 获取商户对应的经销商
     */
    public function get_org_has_agency(){
        $params = input();
        if(isset($params['pid']) && !empty($params['pid'])){
            $pid = $params['pid'];
        }else{
            return json(array('status' => 400, 'info' => '参数错误', 'data' => ''));
        }
        $items = db("org_agency")->where("org_id", $pid)->field("agency_id, agency_name")->select();
        foreach ($items as $k => $v) {
            $items[$k]['agency_name'] = base64_decode($v['agency_name']);
        }
        return json(array('status' => 200, 'info' => 'success', 'data' =>$items));
    }
}
