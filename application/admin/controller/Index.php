<?php
namespace app\admin\controller;

use think\Controller;
use think\Db;
use think\facade\Request;

class Index extends Base
{
    public function index()
    {
        return $this->fetch();
    }

    public function main()
    {
        return $this->fetch();
    }

    /**
     * 修改登录密码
     */
    public function password()
    {
        if(Request::isPost()){
        	$id = session('admin')['id'];
            //原始密码
            $old_password = input('old_password','');
            if(empty($old_password)){
                $this->error('原始密码不能为空！','/Index/password');
            }
            //新密码
            $password = input('password','');
            if(empty($password)){
            	$this->error('密码不能为空','/Index/password');
            }
            //再次确认密码
            $password_confirm = input('password_confirm','');
            if(empty($password_confirm)){
            	$this->error('二次确认密码不能为空','/Index/password');
            }
            //校验俩次密码是否一致
            if($password != $password_confirm){
                $this->error('密码不一致，请重新操作！','/Index/password');
            }
            //获取原始密码进行校验
            $get_old_password = db('admin')->where("id",$id)->value('password');
            // if(getPassword($old_password) != $get_old_password){
            if(!password_verify($old_password, $get_old_password)){
                $this->error('原始密码不一致，请重新操作！','/Index/password');
            }

            //变更密码
            $data['id'] = $id;
            $data['password'] = getPassword($password);
            $data['update_time'] = time();
            if(db('admin')->update($data) === false){
            	$this->error('修改密码失败','/Index/password');
            }

            $this->success('保存成功，请重新登录','/Login/logout');
        }else{
            return $this->fetch();
        }
    }
}