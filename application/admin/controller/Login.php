<?php
namespace app\admin\controller;

use think\Controller;
use think\Db;
use think\facade\Request;
use think\captcha\Captcha;

class Login extends Controller
{
    protected function initialize()
    {
        $site = db('site')->find();
        $this->assign('time',time());
        $this->assign('site',$site);
    }

    public function index()
    {
        if (session(Config('user_auth_key'))) {
            return redirect('/Index/index');
        }
        if(Request::isPost()){
            $email = input('email');
            if(empty($email)){
                $this->error("账号不能为空！");
            }
            $password = input('password');
            if(empty($password)){
                $this->error("密码不能为空！");
            }
            $vertify = input('vertify');
            if(empty($vertify)){
                $this->error("验证码不能为空！");
            }
            //图形验证码判断
            if(!captcha_check($vertify)){
                $this->error('验证码错误');
            }
            $login = model('Admin')->doLogin($email,$password);
            if($login){
                $this->success('登录成功',url('/Index/index'));
            }else{
                $this->error('登录失败',url('/Login/index'));
            }
        }

        return $this->fetch();
    }

    /**
     * [logout description]
     */
    public function logout()
    {
        if (!session(Config('user_auth_key'))) {
            return redirect('/Login/index');
        }
        session(null);
        $this->success('登出成功', url('/Login/index'));
    }

    /**
     * [noAccess description]
     */
    public function noAccess()
    {
        return $this->fetch('no_access');
    }

    /**
     * 验证码获取
     */
    public function vertify()
    {
        $config = array(
            'fontSize' => 22, //字号
            'length' => 4, //字符梳理
            'useCurve' => false,  //混淆曲线
            'useNoise' => false, //杂点
            'bg' => [255, 255, 255]
        );
        $captcha = new Captcha($config);
        $captcha->fontttf = '4.ttf'; 
        return $captcha->entry();
    }
}