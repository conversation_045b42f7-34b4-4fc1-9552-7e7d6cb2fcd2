<?php
namespace app\admin\controller;

use think\Controller;
use think\Db;
use think\facade\Request;

class Site extends Base
{
    /**
     * 网站设置
     */
    public function index()
    {
        //查询配置
    	$site = db('site')->find();
        
        $params = input();
        //新建还是编辑
        $create = true;
        $data = array();
        $data['update_time'] = time();
        if(isset($params['id']) && !empty($params['id'])){
        	$create = false;
            $data['id'] = $params['id'];
        }
        if(Request::isPost()){
        	$data['site_name'] = $params['site_name'];
        	$data['company'] = $params['company'];
            $data['icp'] = $params['icp'];
            $data['copyright'] = $params['copyright'];
        	if($create){
        		$res = db('site')->insert($data);
        	}else{
        		$res = db('site')->update($data);
        	}
    		if($res){
    			$this->success('保存成功', url('index'));
    		}else{
    			$this->success('保存失败', url('index'));
    		}
    		exit;
        }
        $this->assign('site',$site);
        return $this->fetch();
    }
}
