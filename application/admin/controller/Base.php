<?php

namespace app\admin\controller;

use think\Controller;
use \Auth\Auth;

class Base extends Controller
{
    protected function initialize()
    {
        parent::initialize();

        $now = time();

        $Auth = new Auth();

        if (!$Auth->checkLogin()) {
            $this->redirect(url(Config('user_auth_gateway')));
        }
        if (!$Auth->AccessDecision()) {
            $this->redirect(url(Config('user_auth_no_access')));
        }
        // 登录超时
        if (session('admin.overtime') == true && $Auth->checkAccess()) {
            $this->redirect(url('/Login/loginlock'));
        }

        //登录是否过期
        if(session(Config('user_auth_key'))){
            
            $expire = Config('session.session_expire');
            
            //超过时间没有更新，退出登陆
            if(($now - session('last_access_time')) > $expire)
            {
                session(null);
                $this->redirect(url('/Login/logout'));
                exit();
            }

            //刷新最后访问时间
            session('last_access_time', $now);
        }

        // 登录用户信息
        $adminRow = session('admin');
        $this->assign('adminRow', $adminRow);

        // 菜单
        $menuList = model('Admin')->getMenuList();
        $this->assign('menuList', $menuList);

        // 时间戳
        $this->assign('time', $now);

        //站点配置
        $site = db('site')->find();
        $this->assign('site',$site);
    }
}
