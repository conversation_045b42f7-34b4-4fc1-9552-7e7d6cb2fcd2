<?php

namespace app\admin\model;

use think\Model;
use think\Db;
use app\admin\model\Role;
use \Auth\Auth;

class Admin extends Model
{
    /**
     * 登录
     */
    public function doLogin($email, $password)
    {
        $admin = $this->where(array('email' => $email, 'status' => 1))->find();
        if (empty($admin)) {
            return false;
        }
        // if ($admin['password'] != $this->shaByPassword($password)) {
        if(!password_verify($password, $admin['password'])){
            return false;
        }
        $admin['role_name'] = db('role')->where("id = '".$admin['role_id']."'")->value('name');
        $admin['overtime'] = false;

        session('admin', $admin);
        session('last_access_time', time());
        
        $Auth = new Auth();
        $Auth->saveAccessList($admin['id']);
        // var_dump($admin);
        // echo DB::getLastSql();
        // var_dump($Auth);exit;
        return true;
    }

    /**
     * 登录锁定
     */
    public function doLoginLock($password)
    {

        if ($this->shaByPassword($password) == session('admin.password')) {
            session('admin.overtime', false);
            return true;
        } else {
            return false;
        }
    }

    /**
     * [getMenuList description]
     */
    public function getMenuList()
    {
        $roleId = session('admin.role_id');

        //获取权限组
        $roleRow = Role::get($roleId);
        $roles = $roleRow->AuthRule;
        $roles = json_decode(json_encode($roles),true);
        $roleRow['rules'] = $roles;

        // 权限内的菜单
        $authRuleModel = model('AuthRule');
        $roleAuthRuleRows = _array_column($roleRow['rules'], 'id');

        return $authRuleModel->getAuthRuleForRoleAuthRule($roleAuthRuleRows, 0);
    }
}
