<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">   
    <title>登录 - {{:Config('app_name')}}</title>
    <meta name="keywords" content="">
    <meta name="description" content="">
    <link href="__PUBLIC__/css/bootstrap.min.css?v=3.4.0" rel="stylesheet">
    <link href="__PUBLIC__/css/font-awesome.min.css?v=4.3.0" rel="stylesheet">
    <link href="__PUBLIC__/css/animate.min.css" rel="stylesheet">
    <link href="__PUBLIC__/css/login.css?v={{$time}}" rel="stylesheet">
</head>

<body>
    <div class="wrap">
        <div class="">
            <div class="form-data animated fadeInDown">
                <div class="logo-box">
                    <img src="__PUBLIC__/img/logo.png" class="head-logo" />
                </div>

                <div class="login-box">
                    <form class="form1"  method="post" >
                        <div class="p-input relative">
                            <label for="user-name">请输入用户名</label>
                            <input type="text" name="email" id="user-name" />
                            <span class="tel-warn user-name-err hide"><em>账号或密码错误，请重新输入</em><i class="fa fa-warning"></i></span>
                        </div>

                        <div class="p-input relative">
                            <label for="pass">请输入密码</label>
                            <input type="password" name="password" id="pass" />
                            <span class="tel-warn pass-err hide"><em>账号或密码错误，请重新输入</em><i class="fa fa-warning"></i></span>
                        </div>

                        <div class="p-input relative code">
                            <label for="veri">请输入右侧图形验证码</label>
                            <input type="text" name="vertify" id="veri" maxlength="6" />
                            <img class="verify-reset" alt="图形验证码" title="点击刷新验证码" src="{{:url('vertify')}}" />
                            <span class="tel-warn img-err hide"><em>验证码错误，请重新输入</em><i class="fa fa-warning"></i></span>
                        </div>
                        <button class="lang-btn off log-btn" type="submit">登录</button>
                    </form>
                </div>

            </div>
        </div>
        <div class="right animated fadeInDown">
            <p>{{$site.copyright}} {{$site.company}}</p>
            <p>{{$site.icp}}</p>
        </div>
    </div>
    <!-- scripts -->
    <script src="__PUBLIC__/js/jquery.min.js?v=2.1.1"></script>
    <script type="text/javascript">
        $(function () {
            //输入框输入时模拟placeholder效果
            var oInput = $('.form-data input');
            oInput.focus(function () {
                $(this).siblings('label').hide();
            });
            oInput.blur(function () {
                if ($(this).val() == '') {
                    $(this).siblings('label').show();
                }
            });

            var $reset = $('.verify-reset');
            $reset.on('click', function () {
                var t = new Date().getTime();
                $reset[0].src = '/Login/vertify?t=' + t;
            });

            // 登录的回车事件
            $(window).keydown(function (event) {
                if (event.keyCode == 13) {
                    $('.log-btn').trigger('click');
                }
            });

            // 登录的回车事件
            $(document).on('keyup property', 'input', function (event) {
                var userName = $.trim($('#user-name').val());
                var pass = $('#pass').val();
                var code = $('#veri').val();
                if (userName && pass && code) {
                    $('.log-btn').removeClass('off');
                } else {
                    $('.log-btn').addClass('off');
                }
            });

            $('.switch-box').on('click', 'img', function (e) {
                var idx = $(this).data('idx');
                $(this).hide().siblings().show()
                $('.login-box').children().eq(idx).fadeIn(300).siblings().hide()
            });
        });
    </script>      
</body>
</html>
