<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>{{:Config('app_name')}}</title>
    <meta name="keywords" content="">
    <meta name="description" content="">
    <link href="__PUBLIC__/css/bootstrap.min.css?v=3.4.0" rel="stylesheet">
    <link href="__PUBLIC__/css/font-awesome.min.css?v=4.3.0" rel="stylesheet">
    <link href="__PUBLIC__/css/animate.min.css" rel="stylesheet">
    <link href="__PUBLIC__/css/style.min.css?v=3.2.0" rel="stylesheet">
</head>

<body class="gray-bg">

    <div class="wrapper animated fadeInRight">
        
        <div class="ibox">
            <div class="ibox-title">
                <h5>角色列表</h5>
                <div class="ibox-tools">
                    <a id="page-refresh" href="javascript:;">
                        <i class="fa fa-refresh"></i> 刷新
                    </a>
                </div>
            </div>
            <div class="ibox-content">
                <div class="row">
                    <div class="col-sm-12 m-b clearfix">
                        <a class="btn btn-primary pull-right" href="{{:url('info')}}"><i class="fa fa-plus"></i> 添加</a>
                    </div>
                </div>
                
                {{notempty name="groupData"}}
                    <table class="table table-bordered treeview">
                        <!-- <thead>
                            <tr>
                                <th>用户组</th>
                                <th>说明</th>
                                <th class="col-sm-1 text-center">状态</th>
                                <th class="col-sm-1 text-center">操作</th>
                            </tr>
                        </thead> -->
                        <tbody>
                            {{foreach $groupData as $key=>$group }}
                            <tr class="gray-bg">
                                <td colspan="4" class="font-bold">{{$group.name}}</td>
                            </tr>
                            {{foreach $group.child as $key=>$attr }}
                            <tr>
                                <td>{{$attr.name}}</td>
                                <td>{{$attr.remark}}</td>
                                <td class="text-center">
                                    {{if $attr.status == 1}}
                                        <span class="text-navy">启用</span>
                                    {{else/}}
                                        <span class="text-danger">已禁用</span>
                                    {{/if}}
                                </td>
                                <td class="text-center">
                                    <a class="btn btn-xs btn-success" href="{{:url('info',array('id'=>$attr['id']))}}">编辑</a>
                                </td>
                            </tr>
                            {{/foreach}}
                            {{/foreach}}
                        </tbody>
                    </table>
                {{else/}}
                    <h1 class="wrapper text-center"> 
                        <i class="fa fa-4x fa-sitemap"></i>
                        <p class="m-t">暂无角色</p>
                    </h1>
                {{/notempty}}

            </div>
        </div>

    </div>

    <!-- 全局js -->
    <script src="__PUBLIC__/js/jquery.min.js?v=2.1.1"></script>
    <script src="__PUBLIC__/js/bootstrap.min.js?v=3.4.0"></script>

    <!-- 自定义js -->
    <script src="__PUBLIC__/js/content.min.js?v=1.0.0"></script>

</body>
</html>
