<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>{{:Config('app_name')}}</title>
    <meta name="keywords" content="">
    <meta name="description" content="">
    <link href="__PUBLIC__/css/bootstrap.min.css?v=3.4.0" rel="stylesheet">
    <link href="__PUBLIC__/css/font-awesome.min.css?v=4.3.0" rel="stylesheet">
    <link href="__PUBLIC__/css/animate.min.css" rel="stylesheet">
    <link href="__PUBLIC__/css/style.min.css?v=3.2.0" rel="stylesheet">
</head>

<body class="gray-bg">

<div class="wrapper">
    <div class="ibox">
        <div class="ibox-title">

            <h5>域名管理</h5>
            <div class="ibox-tools">
                <a id="page-goback" href="{{:url('index')}}">
                    <i class="fa fa-arrow-left"></i> 后退
                </a>
                <a id="page-refresh" href="javascript:;">
                    <i class="fa fa-refresh"></i> 刷新
                </a>
            </div>
        </div>
        <div class="ibox-content">

            <form class="form-horizontal validate" method="post" action="" autocomplete="off">

                <div class="form-group">
                    <label class="col-sm-2 control-label">商户</label>
                    <div class="col-sm-3">
                        <select class="form-control m-b linkage-org" id="org_id" name="org_id">
                            <option value="">选择商户</option>
                            {{volist name=":get_org_list()" id="v"}}
                                <option value="{{$v['org_id']}}" {{if $v['org_id'] == $row.org_id}}selected='selected'{{/if}}>{{$v['name']}}</option>
                            {{/volist}}
                        </select>
                    </div>
                </div>
                <div class="form-group">
                    <label class="col-sm-2 control-label">经销商</label>
                    <div class="col-sm-3">
                        <!-- <select id="agency_select" name="agency_id[]" data-placeholder="请选择" class="form-control chosen multiple-select" multiple required="required">
                            {{volist name="list_agency" id="v"}}
                            <option value="{{$key}}">{{:base64_decode($v)}}</option>
                            {{/volist}}
                        </select> -->
                        {{empty name = "list_agency"}}
                        <select class="form-control chosen multiple-select linkage-orgagency" name="agency_id[]" multiple>
                            <option value=''>请选择</option>
                        </select>
                        {{else/}}
                        <select class="form-control chosen multiple-select linkage-orgagency" name="agency_id[]" multiple>
                            <option value=''>请选择</option>
                            {{volist name="$list_agency" id="v" }}
                            <option value="{{$key}}" {{if in_array($key,$list_agency_id)}} selected='selected'{{/if}}>{{:base64_decode($v)}}</option>
                            {{/volist}}
                        </select>
                        {{/empty}}
                    </div>
                </div>
                <div class="hr-line-dashed"></div>

                <div class="form-group">
                    <label class="col-sm-2 control-label">域名</label>
                    <div class="col-sm-3">
                        <input type="text" name="url" class="form-control" value="{{$row.url}}" />
                    </div>
                </div>
                <div class="form-group">
                    <label class="col-sm-2 control-label">域名主体</label>
                    <div class="col-sm-3">
                        <input type="text" name="company" class="form-control" value="{{$row.company}}"/>
                    </div>
                </div>
                <div class="form-group">
                    <label class="col-sm-2 control-label">证书到期日</label>
                    <div class="col-sm-3">
                        <input type="text" name="ssl_date" class="form-control calendar mindatepicker" value="{{$row.ssl_date}}" />
                    </div>
                </div>
                <div class="hr-line-dashed"></div>

                <div class="form-group">
                    <label class="col-sm-2 control-label">排序【值大优先】</label>
                    <div class="col-sm-3">
                        <input type="text" name="sort" class="form-control" value="{{$row.sort}}" />
                    </div>
                </div>
                <div class="form-group">
                    <label class="col-sm-2 control-label">备注</label>
                    <div class="col-sm-3">
                        <textarea rows='4' name="remark" class="form-control">{{$row['remark']}}</textarea>
                    </div>
                </div>
                <div class="form-group">
                    <label class="col-sm-2 control-label">启用</label>
                    <div class="col-sm-3">
                        <div class="radio radio-inline radio-success">
                            <input id="status-1" type="radio" name="status" value="1" {{if $row.status == 1}}checked="checked"{{/if}}/>
                            <label for="status-1">是</label>
                        </div>
                        <div class="radio radio-inline radio-danger">
                            <input id="status-0" type="radio" name="status" value="4" {{if $row.status == 4}}checked="checked"{{/if}}/>
                            <label for="status-0">否</label>
                        </div>
                    </div>
                </div>
                <div class="hr-line-dashed"></div>
                <div class="form-group">
                    <div class="col-sm-4 col-sm-offset-2">
                        <input type="hidden" name="id" value="{{$row.id}}">
                        <button type="submit" class="btn btn-primary">保存内容</button>
                    </div>
                </div>
            </form>

        </div>
    </div>

</div>

<!-- 全局js -->
<script src="__PUBLIC__/js/jquery.min.js?v=2.1.1"></script>
<script src="__PUBLIC__/js/bootstrap.min.js?v=3.4.0"></script>

<!-- 自定义js -->
<!-- <script src="__PUBLIC__/js/content.min.js?v={{$time}}"></script> -->
<script src="__PUBLIC__/js-build/content.js?v={{$time}}"></script>
</body>
</html>





            

