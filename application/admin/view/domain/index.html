<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>{{:Config('app_name')}}</title>
    <meta name="keywords" content="">
    <meta name="description" content="">
    <link href="__PUBLIC__/css/bootstrap.min.css?v=3.4.0" rel="stylesheet">
    <link href="__PUBLIC__/css/font-awesome.min.css?v=4.3.0" rel="stylesheet">
    <link href="__PUBLIC__/css/animate.min.css" rel="stylesheet">
    <link href="__PUBLIC__/css/style.min.css?v=3.2.0" rel="stylesheet">
</head>

<body class="gray-bg">

    <div class="wrapper">

        <div class="ibox">
            <div class="ibox-title">
                <h5>域名管理</h5>
                <div class="ibox-tools">
                    <a id="page-refresh" href="javascript:;">
                        <i class="fa fa-refresh"></i> 刷新
                    </a>
                </div>
            </div>
            <div class="ibox-content">
                <div class="row">
                    <div class="col-sm-10 m-b">
                        <form method="get" action="{{:url('index')}}" autocomplete="off">
                            <div class="row">
                                <div class="col-sm-2 m-b">
                                    <div class="input-group">
                                        <span class="input-group-addon gray-bg">状态</span>
                                        <select name="status" class="form-control">
                                            <option value="" >全部</option>
                                            <option value="1" {{if $filter['status'] == '1'}} selected='selected'{{/if}}>启用</option>
                                            <option value="4" {{if $filter['status'] == '4'}} selected='selected'{{/if}}>禁用</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-sm-3 m-b">
                                    <input type="text" class="form-control" placeholder="关键词" name="keyword" value="{{$filter.keyword}}" >
                                </div>
                                <div class="col-sm-2">
                                    <button type="submit" class="btn btn-primary">查询</button>
                                    <a class="btn btn-warning" href="{{:url('index')}}">重置</a>
                                </div>
                            </div>
                        </form>
                    </div>
                    <div class="col-sm-2 m-b clearfix">
                        <a class="btn btn-primary pull-right" href="{{:url('info')}}"><i class="fa fa-plus"></i> 添加</a>
                    </div>
                </div>

                <table class="table table-bordered">
                    <thead>
                    <tr>
                        <th>序号</th>
                        <th>商户</th>
                        <th>经销商</th>
                        <th>域名</th>
                        <th>域名主体</th>
                        <th>SSL证书到期日</th>
                        <th>排序</th>
                        <th>状态</th>
                        <th>备注</th>
                        <th>创建时间</th>
                        <th>更新时间</th>
                        <th class="col-sm-2">操作</th>
                    </tr>
                    </thead>
                    <tbody class="tooltip-fn">
                    {{volist name="rows" id="v"}}
                        <tr>
                            <td class="text-center">{{:get_sequence($pagesize, $i)}}</td>
                            <td>{{notempty name="v.org_id"}}{{$v.org_name}}{{else/}}System{{/notempty}}</td>
                            <td>{{:get_agencyname_byids($v.agency_id)}}</td>
                            <td>{{$v.url}}</td>
                            <td>{{$v.company}}</td>
                            <td>{{$v.ssl_date}}</td>
                            <td>{{$v.sort}}</td>
                            <td>
                                {{if $v['status'] == 1}}
                                    <span class="text-navy">启用</span>
                                {{else/}}
                                    <span class="text-danger">禁用</span>
                                {{/if}}
                            </td>
                            <td>{{$v.remark}}</td>
                            <td>{{$v.create_time|date="Y-m-d"}}</td>
                            <td>{{$v.update_time|date="Y-m-d H:i:s"}}</td>
                            <td>
                                <a class="btn btn-sm btn-success" href="{{:url('info',array('id'=>$v['id']))}}">编辑</a>
                            </td>
                        </tr>
                    {{/volist}}
                    </tbody>
                </table>
                <div class="row">
                    <div class="page-box">
                        {{$page|raw}}
                    </div>
                </div>
            </div>
        </div>

    </div>

    <!-- 全局js -->
    <script src="__PUBLIC__/js/jquery.min.js?v=2.1.1"></script>
    <script src="__PUBLIC__/js/bootstrap.min.js?v=3.4.0"></script>

    <!-- 自定义js -->
    <script src="__PUBLIC__/js/content.min.js?v={{$time}}"></script>
</body>
</html>
