<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>{{:Config('app_name')}}</title>
    <meta name="keywords" content="">
    <meta name="description" content="">
    <link href="__PUBLIC__/css/bootstrap.min.css?v=3.4.0" rel="stylesheet">
    <link href="__PUBLIC__/css/font-awesome.min.css?v=4.3.0" rel="stylesheet">
    <link href="__PUBLIC__/css/animate.min.css" rel="stylesheet">
    <link href="__PUBLIC__/css/style.min.css?v=3.2.0" rel="stylesheet">
</head>

<body class="gray-bg">

<div class="wrapper">
    <div class="ibox">
        <div class="ibox-title">

            <h5>用户管理</h5>
            <div class="ibox-tools">
                <a id="page-goback" href="{{:url('index')}}">
                    <i class="fa fa-arrow-left"></i> 后退
                </a>
                <a id="page-refresh" href="javascript:;">
                    <i class="fa fa-refresh"></i> 刷新
                </a>
            </div>
        </div>
        <div class="ibox-content">

            <form class="form-horizontal validate" method="post" action="" autocomplete="off">

                <div class="form-group">
                    <label class="col-sm-2 control-label">登录邮箱</label>
                    <div class="col-sm-3">
                        <input type="text" name="email" class="form-control" value="{{$adminRowForEdit['email']}}" required />
                    </div>
                </div>

                <div class="form-group">
                    <label for="input-password" class="col-sm-2 control-label">密码</label>
                    <div class="col-sm-3">
                        {{present name="adminRowForEdit"}}
                            <input type="password" class="form-control" name="password" id="input-password" placeholder="不用修改时,请留空！"/>
                        {{else /}}
                            <input type="password" class="form-control" name="password" id="input-password" required/>
                        {{/present}}
                    </div>
                </div>
                
                <div class="form-group">
                    <label for="input-c-password" class="col-sm-2 control-label">确认密码</label>
                    <div class="col-sm-3">
                        {{present name="adminRowForEdit"}}
                            <input type="password" class="form-control" name="password_confirm" data-confirm="#input-password" placeholder="不用修改时,请留空！" data-confirm="#input-password" id="input-c-password" />
                        {{else /}}
                            <input type="password" class="form-control" name="password_confirm" data-confirm="#input-password" id="input-c-password" />
                        {{/present}}

                    </div>
                </div>
                
                <div class="hr-line-dashed"></div>
                <div class="form-group">
                    <label class="col-sm-2 control-label">姓名</label>
                    <div class="col-sm-3">
                        <input type="text" name="name" class="form-control" value="{{$adminRowForEdit.name}}" required />
                    </div>
                </div>
                <div class="form-group">
                    <label class="col-sm-2 control-label">手机号</label>
                    <div class="col-sm-3">
                        <input type="text" name="mobile" class="form-control" value="{{$adminRowForEdit.mobile}}" required/>
                    </div>
                </div>

                <div class="hr-line-dashed"></div>
                <div class="form-group">
                    <label class="col-sm-2 control-label">用户组</label>
                    <div class="col-sm-3">
                        <select class="form-control" name="role_id" id="role_id" required>
                            <option value="0">请选择</option>
                            {{volist name=":getRoleGroupRows()" id="roleRows"}}
                                <optgroup label="{{$roleRows.name}}">
                                    {{volist name="roleRows.child" id="child"}}
                                        <option value="{{$child.id}}" {{if $child.id == $adminRowForEdit['role_id']}} selected='selected'{{/if}}>{{$child.name}}</option>
                                    {{/volist}}
                                </optgroup>
                            {{/volist}}
                        </select>
                    </div>
                </div>
                <div class="hr-line-dashed"></div>
                <div class="form-group">
                    <label class="col-sm-2 control-label">启用</label>
                    <div class="col-sm-3">
                        <div class="radio radio-inline radio-success">
                            <input id="status-1" type="radio" name="status" value="1" {{if $adminRowForEdit.status == 1}}checked="checked"{{/if}}/>
                            <label for="status-1">是</label>
                        </div>
                        <div class="radio radio-inline radio-danger">
                            <input id="status-0" type="radio" name="status" value="0" {{if $adminRowForEdit.status == 0}}checked="checked"{{/if}}/>
                            <label for="status-0">否</label>
                        </div>
                    </div>
                </div>
                <div class="hr-line-dashed"></div>
                <div class="form-group">
                    <div class="col-sm-4 col-sm-offset-2">
                        <input type="hidden" name="id" value="{{$adminRowForEdit.id}}">
                        <button type="submit" class="btn btn-primary">保存内容</button>
                    </div>
                </div>
            </form>

        </div>
    </div>

</div>

<!-- 全局js -->
<script src="__PUBLIC__/js/jquery.min.js?v=2.1.1"></script>
<script src="__PUBLIC__/js/bootstrap.min.js?v=3.4.0"></script>

<!-- 自定义js -->
<script src="__PUBLIC__/js/content.min.js?v={{$time}}"></script>
</body>
</html>





            

