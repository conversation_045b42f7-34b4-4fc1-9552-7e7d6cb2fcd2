<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>{{:Config('app_name')}}</title>
    <meta name="keywords" content="">
    <meta name="description" content="">
    <link href="__PUBLIC__/css/bootstrap.min.css?v=3.4.0" rel="stylesheet">
    <link href="__PUBLIC__/css/font-awesome.min.css?v=4.3.0" rel="stylesheet">
    <link href="__PUBLIC__/css/animate.min.css" rel="stylesheet">
    <link href="__PUBLIC__/css/style.min.css?v=3.2.0" rel="stylesheet">
</head>

<body class="gray-bg">

    <div class="wrapper">

        <div class="ibox">
            <div class="ibox-title">
                <h5>用户管理</h5>
                <div class="ibox-tools">
                    <a id="page-refresh" href="javascript:;">
                        <i class="fa fa-refresh"></i> 刷新
                    </a>
                </div>
            </div>
            <div class="ibox-content">
                <div class="row">
                    <div class="col-sm-10 m-b">
                        <form method="get" action="{{:url('index')}}" autocomplete="off">
                            <div class="row">
                                <div class="col-sm-2 m-b">
                                    <div class="input-group">
                                        <span class="input-group-addon gray-bg">状态</span>
                                        <select name="status" class="form-control">
                                            <option value="1" {{if $filter['status'] == '1'}} selected='selected'{{/if}}>启用</option>
                                            <option value="0" {{if $filter['status'] == '0'}} selected='selected'{{/if}}>禁用</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-sm-3 m-b">
                                    <select name="role_id" class="form-control">
                                        <option value="">用户组</option>
                                        {{volist name=":getRoleGroupRows()" id="roleRows"}}
                                            <optgroup label="{{$roleRows.name}}">
                                                {{volist name="roleRows.child" id="child"}}
                                                    <option value="{{$child.id}}" {{if $child.id == $filter['role_id']}} selected='selected'{{/if}}>{{$child.name}}</option>
                                                {{/volist}}
                                            </optgroup>
                                        {{/volist}}
                                    </select>
                                </div>
                                <div class="col-sm-3 m-b">
                                    <input type="text" class="form-control" placeholder="邮箱、姓名" name="keyword" value="{{$filter.keyword}}" >
                                </div>
                                <div class="col-sm-2">
                                    <button type="submit" class="btn btn-primary">查询</button>
                                    <a class="btn btn-warning" href="{{:url('index')}}">重置</a>
                                </div>
                            </div>
                        </form>
                    </div>
                    <div class="col-sm-2 m-b clearfix">
                        <a class="btn btn-primary pull-right" href="{{:url('info')}}"><i class="fa fa-plus"></i> 添加</a>
                    </div>
                </div>

                <table class="table table-bordered">
                    <thead>
                    <tr>
                        <th>姓名</th>
                        <th>邮箱</th>
                        <th>手机号</th>
                        <th>用户组</th>
                        <th>状态</th>
                        <th class="col-sm-2">操作</th>
                    </tr>
                    </thead>
                    <tbody class="tooltip-fn">
                    {{volist name="adminRows" id="adminRow"}}
                        <tr>
                            <td>{{$adminRow.name}}</td>
                            <td>{{$adminRow.email}}</td>
                            <td>
                                {{notempty name="adminRow.mobile"}}
                                    {{$adminRow.mobile}}
                                {{else /}}
                                    -
                                {{/notempty}}
                            </td>
                            <td>{{$adminRow.role_name}}</td>
                            <td>
                                {{if $adminRow['status'] == 0}}
                                    <span class="text-danger">禁用</span>
                                {{else /}}
                                    <span class="text-navy">启用</span>
                                {{/if}}
                            </td>
                            <td>
                                {{if $adminRow.id != 100}}
                                    <a href="{{:url('info',array('id'=>$adminRow['id']))}}" class="btn btn-sm btn-success">修改</a>
                                    {{if $adminRow['status'] == 1}}
                                        <a class="btn btn-sm btn-danger" href="{{:url('destroy',array('id'=>$adminRow['id'],'status'=>'0'))}}">禁用</a>
                                    {{else/}}
                                        <a class="btn btn-sm btn-info" href="{{:url('destroy',array('id'=>$adminRow['id'],'status'=>'1'))}}">启用</a>
                                    {{/if}}
                                {{else/}}
                                    <span class="btn btn-sm btn-default">无操作</span>
                                {{/if}}
                            </td>
                        </tr>
                    {{/volist}}
                    </tbody>
                </table>

                <div class="row">
                    <div class="page-box">
                        {{$page|raw}}
                    </div>
                </div>

            </div>
        </div>

    </div>

    <!-- 全局js -->
    <script src="__PUBLIC__/js/jquery.min.js?v=2.1.1"></script>
    <script src="__PUBLIC__/js/bootstrap.min.js?v=3.4.0"></script>

    <!-- 自定义js -->
    <script src="__PUBLIC__/js/content.min.js?v={{$time}}"></script>
</body>
</html>
