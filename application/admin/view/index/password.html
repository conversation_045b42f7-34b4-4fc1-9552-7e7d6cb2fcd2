<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>{{:Config('app_name')}}</title>
    <meta name="keywords" content="">
    <meta name="description" content="">
    <link href="__PUBLIC__/css/bootstrap.min.css?v=3.4.0" rel="stylesheet">
    <link href="__PUBLIC__/css/font-awesome.min.css?v=4.3.0" rel="stylesheet">
    <link href="__PUBLIC__/css/animate.min.css" rel="stylesheet">
    <link href="__PUBLIC__/css/style.min.css?v=3.2.0" rel="stylesheet">
    <style>
        .progress-bar_wrap {
            width: 180px;
            height: 5px;
            display: inline-block;
            vertical-align: middle;
            overflow: hidden;
            border-radius: 5px;
        }
        form .progress-bar_item {
            display: inline-block;
            height: 100%;
            width: 31%;
            margin-right: 2%;
            border-radius: 5px;
            float: left;
            -webkit-transition: background-color 0.2s, visisility 0.1s;
            transition: background-color 0.2s, visisility 0.1s;
            background: #f6f6fa;
        }
        form .progress-bar_item-1.active {
            background-color: #ff4b47;
        }
        form .progress-bar_item-2.active {
            background-color: #f9ae35;
        }
        form .progress-bar_item-3.active {
            background-color: #2daf7d;
        }
        .progress-bar_text {
            height: 20px;
        }
    </style>
</head>

<body class="gray-bg">

<div class="wrapper">
    <div class="ibox">
        <div class="ibox-title">

            <h5>用户管理</h5>
            <div class="ibox-tools">
                <a id="page-goback" href="{{:url('/Admin/index')}}">
                    <i class="fa fa-arrow-left"></i> 后退
                </a>
                <a id="page-refresh" href="javascript:;">
                    <i class="fa fa-refresh"></i> 刷新
                </a>
            </div>
        </div>
        <div class="ibox-content">

            <form class="form-horizontal validate" method="post" action="" autocomplete="off">
                <notempty name="act">
                    <div class="form-group">
                        <label class="col-sm-2 control-label">安全提示</label>
                        <div class="col-sm-3">
                            <p class="form-control-static text-danger">您的密码过于简单，请重新设置密码</p>
                        </div>
                    </div>
                </notempty>
                <div class="form-group">
                    <label class="col-sm-2 control-label">登录邮箱</label>
                    <div class="col-sm-3">
                        <input type="text" name="email" class="form-control" value="{{$adminRow['email']}}" disabled />
                    </div>
                </div>
                <div class="form-group">
                    <label for="input-password" class="col-sm-2 control-label">原始密码</label>
                    <div class="col-sm-3">
                        <input type="password" class="form-control" name="old_password" id="old_password" required />
                    </div>
                </div>
                <div class="form-group">
                    <label for="input-password" class="col-sm-2 control-label">密码</label>
                    <div class="col-sm-3">
                        <input type="password" class="form-control" name="password" id="input-password" required />
                        <div class="m-t-sm" style="display: flex; align-items: center">
                            <div class="progress-bar_wrap">
                                <div class="progress-bar_item progress-bar_item-1"></div>
                                <div class="progress-bar_item progress-bar_item-2"></div>
                                <div class="progress-bar_item progress-bar_item-3"></div>
                            </div>
                            <div class="progress-bar_text m-l-sm"></div>
                        </div>
                    </div>
                </div>
                
                <div class="form-group">
                    <label for="input-c-password" class="col-sm-2 control-label">确认密码</label>
                    <div class="col-sm-3">
                        <input type="password" class="form-control" name="password_confirm" data-confirm="#input-password" id="input-c-password" />
                    </div>
                </div>
                <div class="hr-line-dashed"></div>
                <div class="form-group">
                    <div class="col-sm-4 col-sm-offset-2">
                        <button type="submit" class="btn btn-primary">保存内容</button>
                    </div>
                </div>
            </form>

        </div>
    </div>

</div>

<!-- 全局js -->
<script src="__PUBLIC__/js/jquery.min.js?v=2.1.1"></script>
<script src="__PUBLIC__/js/bootstrap.min.js?v=3.4.0"></script>

<!-- 自定义js -->
<script src="__PUBLIC__/js/content.min.js?v={{$time}}"></script>

<script src="__PUBLIC__/js/plugins/validate/jquery.validate.min.js?v={{$time}}"></script>
<script>
    $(function () {
        function changeText(elem, str) {
            elem.html(str);
        }
        function checkStrong(val) {
            var modes = 0;
            if (val.length < 6) return 0;
            if (/\d/.test(val)) modes++; //数字
            if (/[a-z]/.test(val)) modes++; //小写字母
            if (/[A-Z]/.test(val)) modes++; //大写字母
            if (/\W/.test(val)) modes+=2; //特殊字符
            return modes;
        }

        var validator = $('form').validate({
            rules: {
                'old_password': {
                    required: true
                },
                password: {
                    required: true,
                    minlength: 6
                },
                password_confirm: {
                    required: true,
                    equalTo: '#input-password'
                }
            },
            messages: {
                'old_password': {
                    required: '请输入原始密码'
                },
                password: {
                    required: '请输入密码',
                    minlength: '密码长度必须大于6个字符'
                },
                password_confirm: {
                    required: '请输入确认密码',
                    equalTo: '两次密码不一致'
                }
            },
            debug:true,
            submitHandler: function (form) {
                var flag = vertifyPass()
                flag && form.submit();
            }
        });
        $('form').submit(vertifyPass);
        $('[name="password"]').keyup(function () {
            var pbText = $('form .progress-bar_text');
            var level = checkStrong(this.value);
            switch (level) {
                case 0:
                    $('form .progress-bar_item').each(function () {
                        $(this).removeClass('active');
                    });
                    changeText(pbText, '');
                    break;
                case 1:
                    $('form .progress-bar_item-1').addClass('active');
                    $('form .progress-bar_item-2').removeClass('active');
                    $('form .progress-bar_item-3').removeClass('active');
                    changeText(pbText, '弱');
                    break;
                case 2:
                    $('form .progress-bar_item-1').addClass('active');
                    $('form .progress-bar_item-2').addClass('active');
                    $('form .progress-bar_item-3').removeClass('active');
                    changeText(pbText, '中');
                    break;
                default:
                    $('form .progress-bar_item-1').addClass('active');
                    $('form .progress-bar_item-2').addClass('active');
                    $('form .progress-bar_item-3').addClass('active');
                    changeText(pbText, '强');
                    break;
            }
        });

        function vertifyPass() {
            var $passElem = $('[name="password"]');
            var val = $passElem.val(),
                modes = 0;
            if (/\d/.test(val)) modes++; //数字
            if (/[a-z]/.test(val)) modes++; //小写字母
            if (/[A-Z]/.test(val)) modes++; //大写字母
            if (/\W/.test(val)) modes++; //特殊字符

            if (modes < 2) {
                (val.length >= 6) && validator.showErrors({
                    password: '密码强度等级弱，尽量包含：数字、大小写字母及特殊字符'
                });
            }
            if (modes >= 2 && val.length >= 6) {
                $passElem.removeClass('error').addClass('valid');
                $('#input-password-error').html('').hide();
            }
            return modes >= 2 && val.length >= 6;
        }
        $('[name="password"]').blur(vertifyPass);
    });
</script>
</body>
</html>





            

