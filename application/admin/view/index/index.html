﻿<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="renderer" content="webkit">
    <title>{{:Config('app_name')}}</title>
    <meta name="keywords" content="">
    <meta name="description" content="">
    <!--[if IE]>
    <script>
        window.location.href = "/broswer.html";
    </script>
    <![endif]-->
    <link rel="SHORTCUT ICON" href="__PUBLIC__/img/logo.ico" />
    <link href="__PUBLIC__/css/bootstrap.min.css?v=3.4.0" rel="stylesheet">
    <link href="__PUBLIC__/css/font-awesome.min.css?v=4.3.0" rel="stylesheet">
    <link href="__PUBLIC__/css/animate.min.css" rel="stylesheet">
    <link href="__PUBLIC__/css/style.min.css?t={{$time}}" rel="stylesheet">
</head>

<body class="fixed-sidebar full-height-layout gray-bg">
    <div id="wrapper">
        <!--左侧导航开始-->
        <nav class="navbar-default navbar-static-side" role="navigation">
            <div class="nav-close"><i class="fa fa-times-circle"></i></div>
            <div class="sidebar-collapse">
                <ul class="nav" id="side-menu">
                    <li class="nav-header">
                        <div class="profile-element">
                            <img alt="image" class="img-circle" src="__PUBLIC__/img/profile.png" width="60" />
                            <a class="J_menuItem block m-t-xs" href="{{:url('/Index/password')}}">
                                <span class="clear">
                                    <span class="block m-t-xs">
                                        {{$adminRow.name}} [ {{$adminRow.role_name}} ]
                                    </span>
                                    <span class="text-muted text-xs block">
                                        设置 <b class="caret"></b>
                                    </span>
                                </span>
                            </a>
                        </div>
                        <div class="logo-element">后台</div>
                    </li>
                    {{volist name="menuList" id="menu" }}
                    <li>
                        <a href="javascript:;">
                            <i class="fa {{$menu.icon}} fa-fw"></i>
                            <span class="nav-label">{{$menu.title}}</span>
                            <span class="fa arrow"></span>
                        </a>
                        <ul class="nav nav-second-level">
                        {{volist name="menu.child" id="child" }}
                            <li><a class="J_menuItem" href="{{$child.name}}">{{$child.title}}</a></li>
                        {{/volist}}
                        </ul>
                    </li>
                    {{/volist}}
                </ul>
            </div>
        </nav>
        <!--左侧导航结束-->
        <!--右侧部分开始-->
        <div id="page-wrapper" class="gray-bg dashbard-1">
            <div class="row border-bottom">
                <nav class="navbar navbar-static-top" role="navigation" style="margin-bottom: 0">
                    <div class="navbar-header">
                        <a class="navbar-minimalize minimalize-style btn btn-link" href="javascript:;">
                            <i class="fa fa-bars"></i>
                        </a>
                    </div>
                    <ul class="nav navbar-top-links navbar-right">
                        
                    </ul>
                </nav>
            </div>
            <div class="row content-tabs">
                <button class="roll-nav roll-left J_tabLeft"><i class="fa fa-backward"></i></button>
                <nav class="page-tabs J_menuTabs">
                    <div class="page-tabs-content">
                        <a href="javascript:;" class="active J_menuTab" data-id="{{:url('/Index/main')}}">首页</a>
                    </div>
                </nav>
                <button class="roll-nav roll-right J_tabRight"><i class="fa fa-forward"></i>
                </button>
                <div class="btn-group roll-nav roll-right">
                    <button class="dropdown J_tabClose" data-toggle="dropdown">关闭操作<span class="caret"></span></button>
                    <ul role="menu" class="dropdown-menu dropdown-menu-right">
                        <li class="J_tabShowActive"><a>定位当前选项卡</a></li>
                        <li class="divider"></li>
                        <li class="J_tabCloseAll"><a>关闭全部选项卡</a></li>
                        <li class="J_tabCloseOther"><a>关闭其他选项卡</a></li>
                    </ul>
                </div>
                <a href="{{:url('/login/logout')}}" class="roll-nav roll-right J_tabExit"><i class="fa fa fa-sign-out"></i> 退出</a>
            </div>
            <div class="row J_mainContent" id="content-main">
                <iframe class="J_iframe" name="iframe0" width="100%" height="100%" src="{{:url('/Index/main')}}" frameborder="0" data-id="/Index/main" seamless></iframe>
            </div>
            <div class="footer">
                <div class="pull-right">&copy; 2020 </a>
                </div>
            </div>
        </div>
        <!--右侧部分结束-->
        <!--END-->
    </div>

    <!-- 全局js -->
    <script src="__PUBLIC__/js/jquery.min.js?v=2.1.1"></script>
    <script src="__PUBLIC__/js/bootstrap.min.js?v=3.4.0"></script>
    <script src="__PUBLIC__/js/plugins/metisMenu/jquery.metisMenu.js"></script>
    <script src="__PUBLIC__/js/plugins/slimscroll/jquery.slimscroll.min.js?v=1.3.8"></script>
    <script src="__PUBLIC__/js/plugins/layer/layer.min.js"></script>

    <!-- 自定义js -->
    <script src="__PUBLIC__/js/config.min.js?v=1.1.0"></script>
    <script src="__PUBLIC__/js/initialize.min.js?v=1.1.0"></script>

    <!-- 第三方插件 -->
    <script src="__PUBLIC__/js/plugins/pace/pace.min.js"></script>

</body>

</html>