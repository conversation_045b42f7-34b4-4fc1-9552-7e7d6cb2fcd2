var gulp = require('gulp');

// 引入组件
var notify = require('gulp-notify'),
    minifycss = require('gulp-minify-css'), // CSS压缩
    uglify = require('gulp-uglify'),        // js压缩
    concat = require('gulp-concat'),        // 合并文件
    rename = require('gulp-rename'),        // 重命名
    clean = require('gulp-clean');          //清空文件夹


// 合并、压缩、重命名css
gulp.task('stylesheets', function() {

  var array = [];
  array.push('./static/css-build/layout.css');
  array.push('./static/css-build/ui.css');
  array.push('./static/css-build/reset.css');
  array.push('./static/css-build/plugin.css');
  array.push('./static/css-build/loading.css');
  array.push('./static/css-build/media.css');
  array.push('./static/css-build/element.css');
  array.push('./static/css-build/nav.css');

  gulp.src(array)
    .pipe(concat('style.css'))
    //.pipe(gulp.dest('./static/css'))
    .pipe(rename({ suffix: '.min' }))
    .pipe(minifycss())
    .pipe(gulp.dest('./static/css'));

});

// 合并、压缩、重命名文件
gulp.task('javascripts', function() {
  gulp.src('./static/js-build/*.js')
    //.pipe(concat('base.js'))
    //.pipe(gulp.dest('./static/js'))
    .pipe(rename({ suffix: '.min' }))
    .pipe(uglify())
    .pipe(gulp.dest('./static/js'));
});

// clean
gulp.task('clean', function() {
  var array = [];
  array.push('./static/css/style.min.css');
  array.push('./static/js/config.min.js');
  array.push('./static/js/content.min.js');
  array.push('./static/js/product.min.js');
  array.push('./static/js/activity.min.js');
  array.push('./static/js/product.min.js');
  
  return gulp.src(array, {read: false})
    .pipe(clean({force: true}));
});


// dev 监听 js,css
gulp.task('dev',function(){
  gulp.start('javascripts','stylesheets');
  gulp.watch('./static/css-build/*.css', ['stylesheets']);
  gulp.watch('./static/js-build/*.js', ['javascripts']);
});


// defalt 依赖 clean
gulp.task('default',['clean'], function() {
  gulp.start('dev');
});