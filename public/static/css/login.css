@charset "utf-8";


html,body,div,ol,ul,li,dl,dt,dd,h1,h2,h3,h4,h5,h6,input,button,textarea,p,span,table,th,td,form{margin:0;padding:0}
body,input,button,select,textarea{font:12px/1.5 "Microsoft Yahei","Helvetica Neue";color:#34495e;
	-webkit-font-smoothing: antialiased;
  	-moz-osx-font-smoothing: grayscale;
  	-moz-font-feature-settings: "liga","kern";}
table{border-collapse:collapse;border-spacing:0}
img,a img{border:0}

a{color:#369;outline:medium none;text-decoration:none;}
a:hover{text-decoration:none}
label{cursor:pointer}
ul li,.ol li{list-style:none}
em,cite,i{font-style:normal}
p{word-wrap: break-word; word-break: break-all;} 


input:focus, textarea:focus {outline: none;}

input::-ms-clear {display: none;}

textarea {resize: none;}




.cl:after { content: "."; display: block; height: 0; clear: both; visibility: hidden; } .cl { zoom: 1; }


.show { display: block !important; }
.hide { display: none !important; }


.relative { position: relative; }
 



.lang-btn {
	display: inline-block;
	position: relative;
	vertical-align: middle;
	cursor: pointer;
	white-space: nowrap;
	background-color: #3499DA;
	height: 40px;
	line-height: 40px;
	font-size: 16px;
	color: #FFF;
	border: none;
	letter-spacing: 1px;
	overflow: hidden;
	text-align: center;
	border-radius:2px;
}

.lang-cancel{background:0;color:#2d3e50}

.lang-btn-none{background:#e4e9ed;color:#2d3e50;}
.lang-btn-none:hover{background:#e4e9ed;color:#2d3e50;}

.lang-btn-c{display:inline-block;position:relative;vertical-align:middle;cursor:pointer;white-space:nowrap;background:#e4e9ed;height:40px;line-height:40px;
			font-size:16px;color:#2d3e50;border:0;letter-spacing:1px;overflow:hidden;text-align:center;border-radius:2px}

.lang-btn:active{outline:0;box-shadow:none}
.lang-btn:focus{border:0;outline:0;box-shadow:none}
.lang-btn:hover{text-decoration:none}


.lang-btn::-moz-focus-inner {border: 0;padding: 0;}

.lang-btn-success { background-color: #4a993e; }

.lang-btn-eroor { background-color: #b33630; }

.lang-btn-disabled { background-color: #ddd; color: #777; cursor: default; }

.lang-btn-show { background-color: #e4e9ed; color: #2d3e50; cursor: default; }

.lang-btn-relating { background-color: #b8c5ce; color: #2d3e50; }



.lang-btn .lang-btn-content {  padding: 0 30px; display: block; }

.lang-btn-big .lang-btn-content { padding: 0 45px; }

.lang-btn-huge { height: 50px; line-height: 50px; }
.lang-btn-huge .lang-btn-content { padding: 0 45px; }

.lang-btn-col-blue{background:#3499DA;color:#fff;}
.lang-btn-col-blue:hover{background:#3da2e3;color:#fff;}

.lang-btn-fixed-small { width: 100px; }
.lang-btn-fixed-small .lang-btn-content { padding: 0; }

.lang-btn-fixed-big { width: 160px; }
.lang-btn-fixed-big .lang-btn-content { padding: 0; }

.lang-btn-fixed-Large { width: 100%; }
.lang-btn-fixed-Large .lang-btn-content { padding: 0; }


.submit-button{width:80px;height:40px;background:#3499da;color:#fff;font-size:14px;cursor:pointer;line-height:40px;border:0;border-radius:2px}
.submit-button:hover{background:#3da2e3;transition:all .4s ease-in-out 0s}
.submit-button:active{background:#2e93d4}
.mini-button-disabble{width:80px;height:40px;background:#b8c5ce;color:#fff;font-size:14px;cursor:pointer;line-height:40px;border:0;border-radius:2px}
.mini-button-disabble:hover{background:#c8d4db;transition:all .4s ease-in-out 0s}
.mini-button-disabble:active{background:#a9b9c3}



 
.lang-input{padding:14px 15px;height:20px;border:#e4e9ed 1px solid;border-radius:2px;background:#fff;color:#5d6d7e;font-size:16px;line-height:20px;box-sizing: content-box;}

.lang-input:focus {
    border: 1px solid #3498db;
    box-shadow: 0 0 4px rgba(41, 128, 185, 0.4);
}

.lang-input-on, .lang-input-success {
	border: #3498db 1px solid;
	box-shadow: 0 0 4px rgba(41, 128, 185, 0.4);
	color: #34495e;
}

.lang-input-error {
	border: #fcab2b 1px solid;
	box-shadow: 0 0 4px rgba(252, 171, 43, 0.4);
}
 

.h164 { height: 130px; }
.lh20 { line-height: 24px; }

.lang-must{ position: absolute; right: 11px; color: #FD8335!important; line-height: 50px!important;}


 
.login-icon { display: block; height: 40px; width: 100%;  border-bottom: #e9ecee 1px solid; margin-top: 30px; }


.login-on, .login-success { border-bottom: #3498db 1px solid; }
.login-on .log-input, .login-error .log-input, .login-success .log-input { color: #5d6d7e; }
.login-on .iconfont, .login-success .iconfont{color: #3A99D8;}


.login-error { border-bottom: #fcab2b 1px solid; }
.login-error .iconfont{ color: #fcab2b;}


.log-input { border: none; height: 20px; font-size: 16px; padding: 10px 2.5%; line-height: 20px; color:  #b8c4ce; background: #fff; width: 90%;}
			
.log-input-half { border: none; height: 20px; font-size: 16px; padding: 10px 5%; line-height: 20px; color:  #b8c4ce; background: #fff; width: 80%;}
			
.log-input::-moz-placeholder { color: #b8c4ce;}
.log-input::-ms-input-placeholder { color: #b8c4ce;}
.log-input::-webkit-input-placeholder { color: #b8c4ce;}
.log-input:placeholder { color: #b8c4ce;}

textarea::-moz-placeholder { color: #b8c4ce;}
textarea::-ms-input-placeholder { color: #b8c4ce;}
textarea::-webkit-input-placeholder { color: #b8c4ce;}
textarea:placeholder { color: #b8c4ce;}



.globalInfoTip {z-index: 20; height: 60px;width: 100%;position: relative;cursor: pointer;}
.globalInfoTip p {text-align: center;font-size: 16px;height: 30px;line-height: 30px;padding: 15px 0;color: #FFF;position: relative;}
.globalInfoTip .infoTipBack {position: absolute;top: 0;left: 0;right: 0;bottom: 0;background: #3498db;box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);opacity: 0.8;filter:alpha(opacity=80);-moz-opacity: 0.8;-khtml-opacity: 0.8;}
.msg-bor{border-bottom-color:#e74c3c}


[id^="xunlei_com"],[id*="xunlei_com"]{
	display:none !important;
}
body {
	background-color: #3895e8;
}
.wrap {
	box-sizing: border-box;
	height: 100vh;
	background: url('/static/img/bj.jpg') no-repeat center;
	background-size: auto 100vh;
	position: relative;
}
.form-data {
	background-color: #ffffff;
	width: 460px;
	left: 50%;
	margin-left: -230px;
	border-radius: 5px;
	box-shadow: 0 0 30px rgba(0, 0, 0, 0.1);
	padding: 65px 0 30px 0;
	position: fixed;
	top: 15%;
}
.form-data .tel-warn {
	position: absolute;
	color: #ea5d5f;
	font-size: 12px;
	right: 0;
	top: 22px;
}
.form-data .tel-warn i {
	display: inline-block;
	vertical-align: middle;
	color: #ea5d5f;
	font-size: 16px;
	margin-top: -3px;
	margin-left: 5px;
}
.form-data .p-input,
.find_password .p-input {
	padding: 5px 0;
	height: 44px;
	box-sizing: border-box;
	border-bottom: 1px solid #e5e5e5;
	width: 340px;
	margin: 0 auto 16px;
	line-height: 14px;
	display: block;
}
.form-data .code {
	width: 340px;
	margin-left: 60px;
}
.form-data .code input {
	width: 200px;
}
.form-data .code img {
	width: 120px;
	position: absolute;
	right: 0;
	bottom: 2px;
}
.form-data .code .img-err {
	right: 120px;
	top: 15px;
}
.form-data .code a {
	display: inline-block;
	position: absolute;
	width: 80px;
	height: 34px;
	line-height: 34px;
	text-align: center;
	color: #000;
	left: 320px;
	bottom: 0;
}
.form-data .send {
	color: #969696;
	position: absolute;
	right: 0;
	top: 24px;
	z-index: 10;
}
.form-data .send:hover {
	color: #3895e8;
}
.form-data .time {
	color: #969696;
	position: absolute;
	right: 0;
	top: 24px;
	font-size: 14px;
}
.form-data label,
.find_password label {
	font-size: 14px;
	position: absolute;
	display: inline-block;
	color: #cacaca;
  top: 22px;
  font-weight: 400;
}
.form-data input,
.find_password input {
	outline: none;
	border: none;
	z-index: 5;
	position: absolute;
	top: 13px;
	width: 340px;
	background-color: transparent;
	font-size: 20px;
}
input[type='number'] {
	-moz-appearance: textfield;
}
input[type='number']::-webkit-inner-spin-button,
input[type='number']::-webkit-outer-spin-button {
	-webkit-appearance: none;
	margin: 0;
}
.form-data .logo-box{
  position: absolute;
  top: -47px;
  left: 0;
  width: 100%;
  padding-bottom: 20px;
  text-align: center;
}
.form-data .logo-box img{
}

 
.form-data .pass-warn {
	width: 400px;
	color: #ea5d5f;
	font-size: 14px;
	margin: 0 auto;
}
 
.switch-box{
	position: absolute;
	right: 5px;
	top: 5px;
	z-index: 10;
	width: 50px;
	overflow: hidden;
}
.switch-box img{
	height: 30px;
	float: right;
	cursor: pointer;
}

.icon-ok-sign {
	background: #b8c4ce;
	border-radius: 2px;
	margin-right: 5px;
	cursor: pointer;
	overflow: hidden;
	width: 16px;
	height: 16px;
	font-size: 12px;
	display: block;
	margin-top: 7px;
	line-height: 16px;
	text-align: center;
}
.form-data .lang-btn {
	width: 340px;
	font-size: 18px;
	font-weight: bold;
	color: white;
	height: 50px;
	line-height: 50px;
	text-align: center;
	margin: 20px auto;
	display: block;
	border-radius: 5px;
	cursor: pointer;
	user-select: none;
	background-color: #42a5f5;
}
.form-data .lang-btn.off {
	color: #a0a0a0;
	background-color: #e5e5e5;
	pointer-events: none;
}
.bottom-info {
	width: 400px;
	line-height: 18px;
	font-size: 14px;
	color: #cacaca;
	margin: 0 auto 30px;
	text-align: center;
}
.bottom-info a {
	color: #42a5f5;
}
.form-data .error {
	color: #ea5d5f;
	font-size: 14px;
	top: -5px;
	right: 0;
	position: absolute;
}
.form-data .r-forget {
	width: 340px;
	margin: 0 auto;
}
.form-data .r-forget a {
	font-size: 12px;
	color: #8d8d8d;
}
.form-data .r-forget a:hover {
	color: #3895e8;
}
.form-data .third-party {
	width: 400px;
	margin: 0 auto;
	display: flex;
	justify-content: space-around;
}
.form-data .third-party .icon-qq-round {
	font-size: 40px;
	color: #e5e5e5;
	display: inline-block;
	vertical-align: middle;
}
.form-data .third-party .icon-qq-round:hover {
	color: #42a5f5;
}
.form-data .third-party .icon-weixin:hover {
	color: #0fccbc;
}
.form-data .third-party .icon-sina1:hover {
	color: #da2a2e;
}
.form-data .third-party .icon-weixin {
	font-size: 40px;
	color: #e5e5e5;
	display: inline-block;
	vertical-align: middle;
}
.form-data .third-party .icon-sina1 {
	font-size: 36px;
	color: #e5e5e5;
	display: inline-block;
	vertical-align: middle;
}
.form-data .change-login {
	width: 400px;
	margin: 0 auto 10px;
	display: flex;
	justify-content: space-around;
	font-size: 14px;
	color: #cacaca;
}
.form-data .change-login div {
	cursor: pointer;
}
.form-data .change-login div.on {
	color: #76b9f7;
}
 
#pc_reset,
#pc_reset2 {
	width: 280px;
}
.pc_reset span {
	display: inline-block;
	font-size: 14px;
	position: absolute;
	/* color: #cacaca; */
	top: 14px;
	right: 0;
}
 
.wrap .right {
	position: absolute;
	left: 0;
	top: 520px;
	width: 100%;
	text-align: center;
	line-height: 40px;
	color: rgba(0, 0, 0, 0.3);
}
 
@media (max-width: 992px){
   
}