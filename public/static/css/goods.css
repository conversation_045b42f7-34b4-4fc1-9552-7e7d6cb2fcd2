.arguments {
    background-color: #fff;
    padding: 55px 20px;
    margin: 0 auto;
}

.arguments .pro_img {
    float: left;
}

.arguments .arg_details {
    float: right;
    min-height: 300px;
    text-align: left;
}

.arguments h1 {
    font-size: 24px;
    margin-bottom: 10px;
}

.arguments h2 {
    font-size: 14px;
    color: #b5b4b4;
}

.arguments .line {
    height: 1px;
    background-color: #eeeeee;
    margin-top: 10px;
    margin-bottom: 25px;
}

.arguments .remind {
    padding: 10px;
    font-size: 14px;
    line-height: 25px;
    margin-bottom: 20px;
    background-color: #fcf8e3;
    color: #8a6d3b;
    border: 1px solid #faebcc;
}

.arguments .step-title {
    font-size: 16px;
    margin-bottom: 10px;
}

.arguments .step-list {
    min-width: 122px!important;
    height: 40px;
    line-height: 40px;
    padding: 0 10px;
    margin-right: 10px;
    margin-left: 0px;
    margin-bottom: 20px;
    font-size: 14px;
    color: #6c6a6a;
    overflow: hidden;
    padding: 0 10px;
    border-radius: 0px;
    background-color: #fff;
    display: inline-block;
    height: 38px;
    line-height: 38px;
    padding: 0 18px;
    white-space: nowrap;
    text-align: center;
    font-size: 14px;
    border: none;
    border-radius: 2px;
    cursor: pointer;
    outline: 0;
    -webkit-appearance: none;
    transition: all .3s;
    -webkit-transition: all .3s;
    box-sizing: border-box;
    border: 1px solid #C9C9C9;
    outline: 0;
    -webkit-appearance: none;
    transition: all .3s;
    -webkit-transition: all .3s;
    box-sizing: border-box;
}



.arguments .step-list:hover {
    border-color: #fa7416;
    color: #fa7416;
}

.arguments .step-list.active {
    border-color: #fa7416;
    color: #fa7416;
}

.arguments .buy_msg {
    width: 100%;
    padding: 20px 100px 20px 20px;
    background-color: #f9f9fa;
    position: relative;
}

.arguments .selectMsg {
    font-size: 14px;
    color: #8c8c8c;
    margin-bottom: 20px;
}

.arguments .totlePrice {
    font-size: 22px;
    color: #fa7416;
}

.arguments .qq_service {
    width: 100px;
    height: 80px;
    position: absolute;
    top: 13px;
    right: 0;
    border-left: 1px solid #e5e5e5;
}

.arguments .qq_service img {
    display: block;
    margin: 5px auto 0;
}

.arguments .qq_servicep {
    text-align: center;
    margin-top: 10px;
    color: #8c8c8c;
    font-size: 14px;
}



.arguments .goshop {
    margin-top: 35px;
    width: 320px;
    height: 60px;
    font-size: 18px;
    background-color: #fa7416;
    color: #fff;
    border: none;
}
