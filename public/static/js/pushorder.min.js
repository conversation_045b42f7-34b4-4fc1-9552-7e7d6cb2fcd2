$(function(){"use strict";var n=$("#open-zysku-window"),i=$("#zysku-selected"),o=$("#zysku-window"),r=$("#search_zysku"),d=$("#find-zysku"),s=$("#zysku-list"),c=$("#zysku-empty"),l=$("#value-added");n.on("click",function(){o.modal("show")});var t=0;function e(t,e,a){$.ajax({type:"POST",data:{type:t,band:e,time:a},url:"/Ajax/zySkuData",dataType:"JSON",beforeSend:function(){s.empty().parents("table").hide(),c.hide(),d.prop("disabled",!0)},success:function(t){var e=t.status,a=t.data;if(40001==e)window.location.reload();else if(0==e)c.show();else if(1==e)toastr.error("查询结果过多，请完善查询内容"),r.focus();else{s.parents("table").show();for(var n=0,i=a.length;n<i;n++)s.append(p(a[n].zy_code,a[n].zy_sku,a[n].zy_price,a[n].zy_id))}},error:function(){toastr.error("服务异常，请联系管理人员")},complete:function(){d.prop("disabled",!1)}})}o.on("show.bs.modal",function(){0===t&&(e("","",""),t++)}),d.on("click",function(){e($("[name='zy_type']").val(),$("[name='zy_band']").val(),$("[name='zy_time']").val())});function u(t,e,a,n){return'<tr class="excellent-'+t+'" data-type="'+n+'"><td class="text-center">'+(0==n?"宽带套餐":"增值产品")+"</td><td>"+e+'</td> <td class="text-center"><input type="text" name="sku_num[]" class="form-control input-sm text-center" value="1"/></td><td class="text-center">'+a+'</td><th class="text-center col-sm-1"><input type="hidden" name="zy_id[]" value="'+t+'"><a href="javascript:;" class="sku-delete">删除</a></th></tr>'}var p=function(t,e,a,n){return'<tr><td class="text-center">'+t+"</td><td>"+e+"</td><td>"+a+'</td><td class="text-center"><button data-id="'+n+'" data-name="'+e+'" data-price="'+a+'" class="btn btn-xs btn-danger">选择</button></td></tr>'};s.on("click","button",function(){var t=$(this).data("id"),e=$(this).data("name"),a=$(this).data("price");0<$(".excellent-"+t).size()?toastr.warning("此SKU 已经选择"):(o.modal("hide"),l.show(),n.prop("disabled",!0),i.append(u(t,e,a,0)))}),i.on("click",".sku-delete",function(){0==$(this).parents("tr").data("type")?(i.empty(),l.hide(),n.prop("disabled",!1)):$(this).parents("tr").remove()}),$(".js_select_reset").on("click",function(){$(".js_select_choose").val("0").trigger("chosen:updated"),$("#zy_band").html("").append('<option value="0">宽带带宽</option>'),$("#zy_time").html("").append('<option value="0">宽带时长</option>'),e("","","")}),l.on("click",".vap",function(){var t=$(this).data("vap-id"),e=$(this).data("vap-name"),a=$(this).data("vap-price");0<$(".excellent-"+t).size()?toastr.warning("此赠品 已经选择"):i.append(u(t,e,a,1))});var m=$("#open-community-window"),h=$("#community-selected"),y=$("#community-window"),v=$("#search_community"),f=$(".find-community"),b=$("#community-list"),k=$("#community-empty"),w=$("#community-current-page"),g=$("#community-page"),x=$("#last_community"),_=$("#next_community");function z(t,e,a,n){$.ajax({type:"POST",data:{keyword:t,searchtype:e,start:a,limit:n},url:"/Ajax/communityInquiry",dataType:"JSON",beforeSend:function(){b.empty().parents("table").hide(),k.hide(),f.prop("disabled",!0)},success:function(t){var e=t.status,a=t.data.data,t=t.data.rows;if(40001==e)window.location.reload();else if(0==e)k.show();else if(1==e)toastr.error("查询结果过多，请完善查询内容"),v.focus();else if(0==$("#community-current-page").val()&&t<=20?g.hide():g.show(),0<t){k.hide(),b.parents("table").show(),$("#community-pagerows").val(t);for(var n=0,i=a.length;n<i;n++)b.append(D(a[n].bossCommunityAreaId,a[n].communityName,a[n].bossAddress,a[n].servicePointName,a[n].regionName,a[n].repairPointName,a[n].regionId,a[n].repairPointId,a[n].servicePointId,a[n].remark))}else k.show()},error:function(){toastr.error("服务异常，请联系管理人员")},complete:function(){f.prop("disabled",!1)}})}m.on("click",function(){y.modal("show")}),y.on("show.bs.modal",function(){b.children().length||k.show()}),f.on("click",function(){var t=$(this).attr("data-id"),e=$.trim(v.val());if(!e.length)return v.focus(),!1;$("#community-searchtype").val(t),w.val(0),z(e,t,0)}),x.on("click",function(){var t=$("#community-searchtype").val(),e=$.trim(v.val());if(!e.length)return v.focus(),!1;var a=parseInt(w.val())-20;if(a<0)return x.prop("disabled",!0),!1;x.prop("disabled",!1),_.prop("disabled",!1),w.val(a),z(e,t,a)}),_.on("click",function(){var t=$("#community-searchtype").val(),e=$.trim(v.val()),a=$("#community-pagerows").val();if(!e.length)return v.focus(),!1;var n=parseInt(w.val())+20;a<n?_.prop("disabled",!0):_.prop("disabled",!1),x.prop("disabled",!1),w.val(n),z(e,t,n)});var D=function(t,e,a,n,i,o,r,d,s,c){return'<tr><td class="text-center">'+e+"</td><td>"+(a=null==a?"":a)+"</td><td>"+i+"</td><td>"+n+"</td><td>"+(o=null==o?"":o)+"</td><td>"+(c=null==c?"":c)+'</td><td class="text-center"><button data-name="'+e+'"  data-id="'+t+'" data-address="'+a+'" data-service="'+n+'" data-region="'+i+'" data-repairpoint="'+o+'" data-regionid="'+r+'" data-repairpointid="'+d+'" data-servicepointid="'+s+'" data-remark="'+c+'" class="btn btn-xs btn-danger">选择</button></td></tr>'};b.on("click","button",function(){var t=$(this).data("name"),e=$(this).data("id"),a=$(this).data("address"),n=$(this).data("service"),i=$(this).data("region"),o=$(this).data("repairpoint"),r=$(this).data("regionid"),d=$(this).data("repairpointid"),s=$(this).data("servicepointid"),c=$(this).data("remark");y.modal("hide"),m.prop("disabled",!0),h.append('<tr class="community-'+e+'"><td class="text-center">'+t+"</td><td>"+a+'</td><td class="text-center">'+n+'</td><td class="text-center">'+i+'</td><td class="text-center">'+o+'</td><td class="text-center">'+c+'</td><th class="text-center col-sm-1"><input type="hidden" name="community" value="'+t+'"><input type="hidden" name="address" value="'+a+'"><input type="hidden" name="communityId" value="'+e+'"><input type="hidden" name="bureau" value="'+i+'"><input type="hidden" name="bureauCode" value="'+r+'"><input type="hidden" name="repairServicehall" value="'+o+'"><input type="hidden" name="repairServicehallCode" value="'+d+'"><input type="hidden" name="sellServicehall" value="'+n+'"><input type="hidden" name="sellServicehallCode" value="'+s+'"><a href="javascript:;" class="community-delete">删除</a></th></tr>')}),h.on("click",".community-delete",function(){$(this).parents("tr").remove(),m.prop("disabled",!1)});$(".sku-delete").length;var a=1;$(".js_push_btn").on("click",function(){return 0==$("[name='zy_id[]']").length?(_alert("请选取sku"),!1):0==$("[name='communityId']").length?(_alert("请选取社区"),!1):""==$("[name='consult']:selected").val()?(_alert("请选择流程处理"),!1):""==$("[name='appointdate']").val()?(_alert("请选取预约时间"),!1):void swal({title:"推送卓越提示",text:"确认推送卓越后不可更改或撤销，请谨慎操作",type:"warning",showCancelButton:!0,confirmButtonColor:"#DD6B55",confirmButtonText:"确认推送卓越",cancelButtonText:"暂不推送卓越",closeOnConfirm:!1},function(){return 1==a&&(a=2,$(".js_push_excellent").submit(),$(".sa-button-container .confirm").css("background-color","#D0D0D0"),$(".sa-button-container .confirm").on("mouseleave",function(){$(this).css("background-color","#D0D0D0")}),void $(".sa-button-container .confirm").on("mouseenter",function(){$(this).css("background-color","#D0D0D0")}))})})});