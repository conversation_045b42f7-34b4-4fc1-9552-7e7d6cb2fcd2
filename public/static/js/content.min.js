function getCurrentMonthFirst(){var t=new Date;return t.setDate(1),t}function getCurrentMonthLast(){var t=new Date,e=t.getMonth(),e=++e,e=new Date(t.getFullYear(),e,1);return new Date(e-864e5)}function animationHover(t,e){(t=$(t)).hover(function(){t.addClass("animated "+e)},function(){window.setTimeout(function(){t.removeClass("animated "+e)},2e3)})}function toDecimal(t){var e=parseFloat(t);if(!isNaN(e))return Math.round(100*t)/100}function _alert(t,e){swal({title:"",text:t,timer:2e3,type:e,showConfirmButton:!1})}function loadScript(t,e){var i=document.createElement("script");i.type="text/javascript",void 0!==e&&(i.readyState?i.onreadystatechange=function(){"loaded"!=i.readyState&&"complete"!=i.readyState||(i.onreadystatechange=null,e())}:i.onload=function(){e()}),i.src=t,document.body.appendChild(i)}jQuery(function(e){var n=e.ajax;e.ajax=function(t){var i=t&&t.success||function(t,e){},t=e.extend(t,{success:function(t,e){40001!==t.status?i(t,e):window.location.reload()}});n(t)}}),Date.prototype.Format=function(t){var e,i={"M+":this.getMonth()+1,"d+":this.getDate(),"h+":this.getHours(),"m+":this.getMinutes(),"s+":this.getSeconds()};for(e in/(y+)/.test(t)&&(t=t.replace(RegExp.$1,(this.getFullYear()+"").substr(4-RegExp.$1.length))),i)new RegExp("("+e+")").test(t)&&(t=t.replace(RegExp.$1,1==RegExp.$1.length?i[e]:("00"+i[e]).substr((""+i[e]).length)));return t},Array.prototype.indexOf=function(t){for(var e=0;e<this.length;e++)if(this[e]==t)return e;return-1},Array.prototype.remove=function(t){t=this.indexOf(t);-1<t&&this.splice(t,1)},function(n){var t;(t=$(".treeview-collapse")).delegate(".L1 > .T1","click",function(){$(".L2, .L3").hide(),$(this).parents("tr").nextUntil(".L1",".L2").show()}),t.delegate(".L2 > .T2","click",function(){$(".L3").hide(),$(this).parents("tr").nextUntil(".L2",".L3").show()}),0<$("[class*=calendar]").size()&&loadScript("/static/js/plugins/layer/laydate/laydate.js",function(){$("[class*=calendar]").each(function(){$(this).prop("readonly",!0),-1!=$(this).val().indexOf("1970-01-01")&&$(this).val("")}),lay(".calendar").each(function(){var t;$(this).hasClass("timepicker")?laydate.render({elem:this,type:"datetime",trigger:"click"}):$(this).hasClass("timeminipicker")?(laydate.render({elem:this,type:"datetime",trigger:"click"}),(t=document.createElement("link")).href="/static/css-build/timeminipicker.css",t.rel="stylesheet",t.type="text/css",$("head").append(t)):laydate.render({elem:this,trigger:"click"})})}),0<$(".tooltip-fn").size()&&($(".tooltip-fn").tooltip({selector:"[data-toggle=tooltip]",container:"body"}),$(".tooltip-fn").popover({selector:"[data-toggle=popover]",container:"body"})),0<$("form.validate").size()&&$.ajax({url:"/static/js/plugins/validate/jquery.validate.min.js",dataType:"script",cache:!0,success:function(){$.validator.setDefaults({highlight:function(t){$(t).closest(".form-group").addClass("has-error")},success:function(t){t.closest(".form-group").removeClass("has-error")},errorElement:"span",errorPlacement:function(t,e){e.is(":radio")||e.is(":checkbox")||e.parent().is(".input-group")?t.appendTo(e.parent().parent()):t.appendTo(e.parent())},errorClass:"help-block m-b-none",validClass:""}),$(".validate").validate()}}),$("<link/>").attr({rel:"stylesheet",href:"/static/css/plugins/sweetalert/sweetalert.css"}).insertBefore("head > link:last"),$.ajax({url:"/static/js/plugins/sweetalert/sweetalert.min.js",dataType:"script",cache:!0}),0<$("select.chosen").size()&&($("<link/>").attr({rel:"stylesheet",href:"/static/css/plugins/chosen/chosen.css"}).insertBefore("head > link:last"),$.ajax({url:"/static/js/plugins/chosen/chosen.jquery.min.js",dataType:"script",cache:!0,success:function(){$(".chosen").chosen({width:"100%",search_contains:!0,no_results_text:"没有查询到相关数据"})}})),$("#page-refresh").click(function(){n.location.reload()}),(0<$(".checkbox").size()||0<$(".radio").size())&&$("<link/>").attr({rel:"stylesheet",href:"/static/css/plugins/awesome-bootstrap-checkbox/awesome-bootstrap-checkbox.css"}).insertBefore("head > link:last"),$(".check-all").on("click",function(){$(this).parents("thead").siblings("tbody").find(":checkbox:not(:disabled)").prop("checked",$(this).is(":checked"))}),$(".check-all-form").on("submit",function(){if($(".check-child:checked").size()<1)return _alert("请勾选至少一条数据","error"),!1}),$("<link/>").attr({rel:"stylesheet",href:"/static/css/plugins/toastr/toastr.min.css"}).insertBefore("head > link:last"),$.ajax({url:"/static/js/plugins/toastr/toastr.min.js",dataType:"script",cache:!0,success:function(){toastr.options={progressBar:!0,positionClass:"toast-top-right",showDuration:"500",hideDuration:"500",timeOut:"2000",extendedTimeOut:"500",showEasing:"swing",hideEasing:"linear",showMethod:"fadeIn",hideMethod:"fadeOut"}}}),0<$('input[type="file"]').size()&&$.ajax({url:"/static/js/plugins/bootstrap-prettyfile/bootstrap-prettyfile.js",dataType:"script",cache:!0,success:function(){$('input[type="file"]').prettyFile({text:"选择文件"})}}),$(".modal").appendTo("body"),$(".fn-confirm, .fn-delete, .fn-do").on("click",function(t){var e=$(this),i=e.text();swal({title:i+"提示",text:"确认"+i+"后不可更改或撤销，请谨慎操作",type:"warning",showCancelButton:!0,confirmButtonColor:"#DD6B55",confirmButtonText:"确认"+i,cancelButtonText:"暂不"+i,closeOnConfirm:!1},function(){n.location.href=e.attr("data-link")})}),$('input[type="number"]').on("wheel",function(){return!1}),$("#province_id").on("change",function(){var t=$(this).val(),i=$("#city_id"),e=$("#district_id");i.html('<option value="">选择城市</option>'),e.html('<option value="">选择地区</option>'),"0"!=t&&""!=t&&$.ajax({type:"POST",data:"is_show=1&province_id="+t,url:"/Ajax/get_citys",dataType:"JSON",success:function(t){$.each(t,function(t,e){i.append('<option value="'+t+'">'+e+"</option>")})}})}),$("#city_id").on("change",function(){var t=$(this).val(),i=$("#district_id");i.html('<option value="">选择地区</option>'),"0"!=t&&""!=t&&$.ajax({type:"POST",data:"is_show=1&city_id="+t,url:"/Ajax/get_districts",dataType:"JSON",success:function(t){$.each(t,function(t,e){i.append('<option value="'+t+'">'+e+"</option>")})}})})}(window);