/*! multi.js 14-04-2017 */
var multi=function(){var a=function(a,b){var c=document.createEvent("HTMLEvents");c.initEvent(a,!1,!0),b.dispatchEvent(c)},b=function(b,c){var d=b.options[c.target.getAttribute("multi-index")];d.disabled||(d.selected=!d.selected,a("change",b))},c=function(a,b){if(a.wrapper.selected.innerHTML="",a.wrapper.non_selected.innerHTML="",a.wrapper.search)var c=a.wrapper.search.value;for(var d=0;d<a.options.length;d++){var e=a.options[d],f=e.value,g=e.textContent||e.innerText,h=document.createElement("a");if(h.tabIndex=0,h.className="item",h.innerHTML=g,h.setAttribute("role","button"),h.setAttribute("data-value",f),h.setAttribute("multi-index",d),e.disabled&&(h.className+=" disabled"),e.selected){h.className+=" selected";var i=h.cloneNode(!0);a.wrapper.selected.appendChild(i)}(!c||c&&g.toLowerCase().indexOf(c.toLowerCase())>-1)&&a.wrapper.non_selected.appendChild(h)}},d=function(a,d){if(d="undefined"!=typeof d?d:{},d.enable_search="undefined"==typeof d.enable_search||d.enable_search,d.search_placeholder="undefined"!=typeof d.search_placeholder?d.search_placeholder:"Search...",null==a.dataset.multijs&&"SELECT"==a.nodeName&&a.multiple){a.style.display="none",a.setAttribute("data-multijs",!0);var e=document.createElement("div");if(e.className="multi-wrapper",d.enable_search){var f=document.createElement("input");f.className="search-input",f.type="text",f.setAttribute("placeholder",d.search_placeholder),f.addEventListener("input",function(){c(a,d)}),e.appendChild(f),e.search=f}var g=document.createElement("div");g.className="non-selected-wrapper";var h=document.createElement("div");h.className="selected-wrapper",e.addEventListener("click",function(c){c.target.getAttribute("multi-index")&&b(a,c)}),e.addEventListener("keypress",function(c){var d=32===c.keyCode||13===c.keyCode,e=c.target.getAttribute("multi-index");e&&d&&(c.preventDefault(),b(a,c))}),e.appendChild(g),e.appendChild(h),e.non_selected=g,e.selected=h,a.wrapper=e,a.parentNode.insertBefore(e,a.nextSibling),c(a,d),a.addEventListener("change",function(){c(a,d)})}};return d}();"undefined"!=typeof jQuery&&!function(a){a.fn.multi=function(b){return b="undefined"!=typeof b?b:{},this.each(function(){var c=a(this);multi(c.get(0),b)})}}(jQuery);