!function(t){t.fn.extend({prettyFile:function(n){function e(t,n){return t.wrap("<div></div>"),t.hide(),t.after('				<div class="input-append input-group"">					<span class="input-group-btn">						<button class="btn btn-default" type="button">'+n+'</button>					</span>					<input class="input-large form-control" type="text">				</div>				'),t.parent()}function i(n,e){n.find('input[type="file"]').change(function(){var i=t(this)[0].files,p="";if(0==i.length)return!1;if(e&&1!=i.length)i.length>1&&(p=i.length+" files selected");else{var u=t(this).val().split("\\");p=u[u.length-1]}n.find(".input-append input").val(p)})}function p(t,n){t.find(".input-append").click(function(n){n.preventDefault(),t.find('input[type="file"]').click()})}var u={text:"Select Files"},n=t.extend(u,n),l=this;return l.each(function(){if($this=t(this),$this){var u=$this.attr("multiple");$wrap=e($this,n.text),i($wrap,u),p($wrap)}})}})}(jQuery);