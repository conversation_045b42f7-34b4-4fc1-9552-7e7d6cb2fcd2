$(function(){"use strict";$("#PL");var t=initFindSku({chooseBtn:$("#NEWS-CHOOSE-BTN"),chooseWin:$("#NEWS-CHOOSE-WINDOW"),btnText:"PRODUCT",requestUrl:"/Ajax/get_wx_news",htmlTemplate:function(t,e,a,n,s,r,i){return'<tr><td class="text-center">'+e+'</td><td class="text-center">'+a+"</td><td>"+n+' <a href="'+s+'" target="_blank"><i class="fa fa-share"></i></a></td><td class="text-center"><a href="'+r+'" target="_blank"><img width="100" height="70" src="'+r+'"/></a></td><td class="text-center"><button type="button" class="btn btn-sm btn-danger" data-text-id="'+e+'">'+i+"</button></td></tr>"},successFunc:function(t,e){var a=e.status,n=e.data;if(1==a){$("#SKU-LIST").parents("table").show();for(var s=0,r=n.length;s<r;s++){var i=n[s].link;$("#SKU-LIST").append(this.htmlTemplate(s,n[s].id,n[s].title,n[s].intro,n[s].url,i,t))}}else $("#EMPTY").show()}}),e=t.LIST,a=t.WINDOW;e.on("click","button",function(){var t=$(this).attr("data-text-id");$("#image_material").val(t),a.modal("hide")})}(window));