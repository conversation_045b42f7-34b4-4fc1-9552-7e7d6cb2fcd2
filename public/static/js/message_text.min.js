$(function(){"use strict";$("#PL");var t=initFindSku({chooseBtn:$("#TEXT-CHOOSE-BTN"),chooseWin:$("#TEXT-CHOOSE-WINDOW"),btnText:"PRODUCT",requestUrl:"/Ajax/get_wx_text",htmlTemplate:function(t,n,e,a){return'<tr><td class="text-center">'+n+"</td><td>"+e+'</td><td class="text-center"><button type="button" class="btn btn-sm btn-danger" data-text-id="'+n+'" data-content="'+e+'">'+a+"</button></td></tr>"},successFunc:function(t,n){var e=n.status,a=n.data;if(1==e){$("#SKU-LIST").parents("table").show();for(var s=0,o=a.length;s<o;s++){a[s].link;$("#SKU-LIST").append(this.htmlTemplate(s,a[s].id,a[s].content,t))}}else $("#EMPTY").show()}}),n=t.LIST,e=t.WINDOW;n.on("click","button",function(){$(this).attr("data-text-id");var t=$(this).attr("data-content");$("textarea#content").val(t),e.modal("hide")})}(window));