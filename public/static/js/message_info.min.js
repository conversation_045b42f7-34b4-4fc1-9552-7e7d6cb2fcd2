function choose_top(){"Welcome"==$('select[name="addons"]').val()?$("#top_show").hide():$("#top_show").show()}function initFindSku(o){var t,n=o.chooseBtn,e=o.chooseWin,i=o.btnText,a=e.find('input[name="token"]'),r=e.find('input[name="keyword"]'),c=$("#SKU-FIND-BTN"),l=$("#SKU-LIST"),s=$("#EMPTY"),d=0;function u(n,e){$.ajax({type:"POST",data:"keyword="+e+"&token="+n,url:o.requestUrl,dataType:"JSON",beforeSend:function(){l.parents("table").hide(),s.hide(),l.empty(),c.prop("disabled",!0)},success:function(n){o.successFunc(t,n)},error:function(){toastr.error("服务异常，请联系管理人员")},complete:function(){c.prop("disabled",!1)}})}"PRODUCT"===i?t="选择":$(".wrapper").html("<h1>模块 "+MOD+" 初始化失败，请刷新重试</h1>"),n.on("click",function(){e.modal("show"),0===d&&(u($.trim(a.val()),$.trim(r.val())),d++)}),c.on("click",function(){return $.trim(a.val()).length||$.trim(r.val()).length?void u($.trim(a.val()),$.trim(r.val())):(r.focus(),!1)}),r.on("keypress",function(n){13==n.keyCode&&$.trim(r.val()).length&&u($.trim(a.val()),$.trim(r.val()))}),$("#app_id").on("change",function(){var n=$(this).val();$("#token").val(n),u(n,"")});n={};return n.LIST=l,n.WINDOW=e,n}$('select[name="addons"]').change(function(){choose_top()}),choose_top();