function ids_sort(t,a){if(t.length){t=-1<t.indexOf("_")?t.split("_"):[t];return t.push(a),t.sort().join("_")}return""+a}function group_in_array(t,r){return t.findIndex(function(t,a){return t.group_name==r})}function attr_in_array(t,r){return t.findIndex(function(t,a){return t.attr_name==r})}function exec_recursion(t){var a=t.length;if(2<=a){for(var r=t[0].length,e=t[1].length,n=new Array(r*e),o=0,i=0;i<r;i++)for(var u=0;u<e;u++)t[0][i]instanceof Array?n[o]=t[0][i].concat(t[1][u]):n[o]=[t[0][i]].concat(t[1][u]),o++;for(var s=new Array(a-1),i=2;i<a;i++)s[i-1]=t[i];return s[0]=n,exec_recursion(s)}return t[0]}$(function(t){"use strict";var l=[],m=$("#attribute_table"),h=m.find("thead"),g=m.find("tbody"),f=$("#createProduct").val();$("#site_id").on("change",function(){var t=$(this).val(),a=$('input[name="product_code"]'),r=new Date;101!=t?102!=t?a.val(""):a.val("CKP-"+r.Format("yyyyMMdd-hms")):a.val("KDP-"+r.Format("yyyyMMdd-hms"))});var s=$("#attr-group-data"),a=$("#category_id"),e=$("#brand_id");a.on("change",function(){l=[],s.empty(),h.empty(),g.empty(),e.html('<option value="">选择品牌</option>'),a.val().length&&$.ajax({type:"POST",data:"cat_id="+$(this).val(),url:"/Ajax/get_brands_by_category",dataType:"json",success:function(t){var r="";$.each(t,function(t,a){r+='<option value="'+a.brand_id+'">'+a.brand_name+"</option>"}),e.append(r)}})}),e.on("change",function(){l=[],s.empty(),h.empty(),g.empty(),e.val().length&&$.ajax({type:"POST",data:"cate_id="+a.val()+"&brand_id="+$(this).val(),url:"/Ajax/get_attr_group",dataType:"JSON",beforeSend:function(){},success:function(t){if(200==t.status){for(var a=t.data,r="",e=0,n=a.length;e<n;e++){r+="<tr>",r+='<td class="text-center">',r+=a[e].group_name,r+="</td><td>";for(var o=0,i=a[e].attr.length;o<i;o++){var u=a[e].attr[o];r+='<div class="col-sm-4">',r+='<div class="checkbox checkbox-success">',r+='<input id="check-attr-'+u.attr_id+'" type="checkbox" name="attr_id[]" class="check-attr" value="'+u.attr_id+'" data-group-id="'+a[e].group_id+'" data-group-name="'+a[e].group_name+'"" data-attr-id="'+u.attr_id+'" data-attr-name="'+u.attr_name+'" />',r+='<label for="check-attr-'+u.attr_id+'">'+u.attr_name+"</label>",r+="</div>",r+="</div>"}r+="</td>"}s.empty().append(r)}else toastr.error(t.info)},error:function(){alert("服务异常，请联系管理人员")},complete:function(){}})});var r=$('input[name="attr_id[]"]:checked');0<r.size()&&$.each(r,function(){var t=$(this),a={group_id:t.data("group-id"),group_name:t.data("group-name"),group_attr:[]},t={attr_id:t.data("attr-id"),attr_name:t.data("attr-name")};-1==group_in_array(l,a.group_name)&&l.push(a);a=group_in_array(l,a.group_name);-1==attr_in_array(l[a].group_attr,t.attr_name)&&l[a].group_attr.push(t)}),$(document).on("click",".check-attr",function(){var t=$(this),a=t.prop("checked"),r={group_id:t.data("group-id"),group_name:t.data("group-name"),group_attr:[]},t={attr_id:t.data("attr-id"),attr_name:t.data("attr-name")};if(a){if("activityProduct"==f&&1<$(this).parents("tr").find(".check-attr:checked").size())return _alert("秒杀或拼团商品最多只能有一个SKU","error"),$(this).prop("checked",!1),!1;-1==group_in_array(l,r.group_name)&&l.push(r);var e=group_in_array(l,r.group_name);-1==attr_in_array(l[e].group_attr,t.attr_name)&&l[e].group_attr.push(t)}else{e=group_in_array(l,r.group_name),t=attr_in_array(l[e].group_attr,t.attr_name);-1<t&&(l[e].group_attr.splice(t,1),l[e].group_attr.length||l.splice(e,1))}for(var n=[],o=[],i=0,u=l.length;i<u;i++){var s=l[i];n.push([s.group_id,s.group_name]),o.push([]);for(var c=0,d=s.group_attr.length;c<d;c++){var p=[],_={attr_id:s.group_attr[c].attr_id,attr_name:s.group_attr[c].attr_name,group_id:s.group_id};p.push(_),o[i].push(p)}}n.length&&o.length?(function(t){var a="<tr>";t.sort(function(t,a){return t[0]-a[0]});for(var r=0,e=t.length;r<e;r++)a+="<th>"+t[r][1]+"</th>";a+='<th class="col-sm-2 text-center">提成</th>',a+='<th class="col-sm-2 text-center">销售价</th>',a+='<th class="col-sm-1 text-center">状态</th>',a+="</tr>",h.empty().append(a)}(n),function(t){for(var a="",r=0,e=t.length;r<e;r++){var n="";a+="<tr>",t[r].sort(function(t,a){return t.group_id-a.group_id});for(var o=0,i=t[r].length;o<i;o++)a+="<td>"+t[r][o].attr_name,a+="</td>",n=ids_sort(n,t[r][o].attr_id);a+='<td><input name="costs[]" type="text" class="form-control text-center" value="0" /></td>',a+='<td><input name="prices[]" type="text" class="form-control text-center" value="0" /></td>',a+="activityProduct"==f?'<td class="text-center">可售':'<td class="text-center"><input type="checkbox" name="soldout[]"> 无货',a+='<input type="hidden" name="sku_status[]" value="1" />',a+='<input type="hidden" class="sku-'+n+'" name="attr_ids[]" value="'+n+'" />',a+="</td>",a+="</tr>"}g.empty().append(a)}(exec_recursion(o)),function(){var t=$('input[name="items_json"]').val();if(t.length)try{t=JSON.parse(t);for(var a=0,r=t.length;a<r;a++){var e=m.find(".sku-"+t[a].attr_ids).parents("tr");e.find('input[name="costs[]"]').val(t[a].base_price),e.find('input[name="prices[]"]').val(t[a].shop_price),e.find('input[name="sku_status[]"]').val(t[a].status),2==t[a].status&&(e.find('input[name="soldout[]"]').prop("checked",!0),e.addClass("sku-soldout"))}}catch(t){toastr.warning(t.message)}}()):(h.empty(),g.empty())}),g.on("click",'input[name="soldout[]"]',function(){var t=$(this).parents("tr");$(this).prop("checked")?(t.find('input[name="sku_status[]"]').val(2),t.addClass("sku-soldout")):(t.find('input[name="sku_status[]"]').val(1),t.removeClass("sku-soldout"))}),g.on("click","a.delete",function(){confirm("是否确认删除此属性商品？")&&$(this).parents("tr").remove()})});