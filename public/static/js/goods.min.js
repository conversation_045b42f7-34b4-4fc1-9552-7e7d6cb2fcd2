$(function(){var s=$("#product_img"),e=$(".js_type"),i=$(".js_type_list"),d=$(".js_selectMsg"),c=$(".js_totlePrice"),n=$(".goshop"),o=$(".js_arg_details");function a(){var a=[];e.each(function(){$(this).find(i).each(function(){var t;$(this).hasClass("active")&&(t=$(this).attr("data-id"),a.push(t))})});var t=$("#product_id").val();$("#group_id").val(a.join("_")),$.ajax({type:"POST",url:"/Ajax/productPrice",data:{group:a,product_id:t},dataType:"json",beforeSend:function(){o.off("click")},success:function(t){200==t.status?(d.find("span").text(t.data.attrs_name),c.text("总计："+t.data.shop_price+"元"),n.attr("disabled",!1).removeClass("layui-btn-disabled")):(n.attr("disabled",!0).addClass("layui-btn-disabled"),c.text("暂无商品")),s.attr("src",t.data.sku_img||s.attr("data-src"))},error:function(){n.attr("disabled",!0).addClass("layui-btn-disabled"),c.text("暂无商品")},complete:function(){o.on("click",".js_type_list",function(t){chooseType(t.target)})}})}chooseType=function(t){if($(t).hasClass("active"))return!1;$(t).addClass("active").siblings().removeClass("active"),a()},a()});