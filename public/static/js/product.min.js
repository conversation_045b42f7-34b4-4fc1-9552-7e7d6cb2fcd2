$(function(){"use strict";var r=$("#attribute_table"),a=(r.find("thead"),r.find("tbody"));$("form").on("submit",function(){var r=a.find("tr");if(0==r.size())return toastr.error("至少选择一款单品，才能创建商品套餐"),!1;for(var t=0,i=r.size();t<i;t++){var n=r.eq(t).find('input[name="sku_status[]"]'),e=r.eq(t).find('input[name="prices[]"]');if(1==n.val()&&0==e.val())return toastr.error("商品销售价不能为0"),$.scrollTo("#tbody",800),!1}})}(window));