$(function(){"use strict";var t=$('input[name="is_long"]'),e=$("#time-range");t.on("click",function(){1==$(this).val()?e.hide():e.show()});var n=$("#open-coupon-window"),c=$("#coupon-selected"),a=$("#coupon-window"),r=$("#search-coupon"),o=$("#find-coupon"),s=$("#coupon-list"),i=$("#coupon-empty"),d=$("#site_id").val();n.on("click",function(){a.modal("show")});var u=0;function l(t,e){$.ajax({type:"POST",data:"keyword="+t+"&site_id="+e,url:"/Ajax/get_coupons",dataType:"JSON",beforeSend:function(){s.empty().parents("table").hide(),i.hide(),o.prop("disabled",!0)},success:function(t){var e=t.status,n=t.data;if(40001==e)window.location.reload();else if(0==e)i.show();else if(1==e)toastr.error("查询结果过多，请完善查询内容"),r.focus();else{s.parents("table").show();for(var a=0,o=n.length;a<o;a++)s.append(h(a,n[a].coupon_id,n[a].type,n[a].discount,n[a].total,n[a].reduce))}},error:function(){toastr.error("服务异常，请联系管理人员")},complete:function(){o.prop("disabled",!1)}})}a.on("show.bs.modal",function(){0===u&&(l("",d),u++)}),o.on("click",function(){var t=$.trim(r.val());if(!t.length)return r.focus(),!1;l(t,d)});function p(t){switch(parseInt(t)){case 1:return"折扣券";case 2:return"满减券";default:return"无门槛券"}}function f(t,e,n,a){switch(parseInt(t)){case 1:return 100*e+" 折";case 2:return"满"+n+"元减"+a+"元";default:return a+"元无门槛券"}}var h=function(t,e,n,a,o,c){return'<tr><td class="text-center">'+(t+=1)+"</td><td>"+p(n)+"</td><td>"+f(n,a,o,c)+'</td><td class="text-center"><button data-id="'+e+'" data-type="'+n+'" data-discount="'+a+'" data-total="'+o+'" data-reduce="'+c+'" class="btn btn-xs btn-danger">选择</button></td></tr>'};s.on("click","button",function(){var t=$(this).data("id"),e=$(this).data("type"),n=$(this).data("discount"),a=$(this).data("total"),o=$(this).data("reduce");0<$(".coupon-"+t).size()?toastr.warning("活动中已包含此优惠券"):c.append((n=n,a=a,o=o,'<tr class="coupon-'+(t=t)+'"><td class="text-center">'+p(e=e)+"</td><td>"+f(e,n,a,o)+'</td><td class="text-center"><input type="text" name="max[]" class="form-control text-center" value="0" required /> </td> <td class="text-center"><input type="text" name="limit[]" class="form-control text-center" value="0" required /></td><th class="text-center col-sm-1"><input type="hidden" name="coupon_id[]" value="'+t+'"><a href="javascript:;" class="coupon-delete">删除</a></th></tr>'))}),c.on("click",".coupon-delete",function(){$(this).parents("tr").remove()});var t=$('input[name="range"]'),m=$("#used-range"),n=$("#used-range-selector");t.on("click",function(){1==$(this).val()?m.hide():m.show()}),n.multi({enable_search:!0,search_placeholder:"可输入 商品名称、SKU属性 检索"})});