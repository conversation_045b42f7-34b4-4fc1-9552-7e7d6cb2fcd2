$(function(){"use strict";$("#PL");var t=initFindSku({chooseBtn:$("#IMAGE-CHOOSE-BTN"),chooseWin:$("#IMAGE-CHOOSE-WINDOW"),btnText:"PRODUCT",requestUrl:"/Ajax/get_wx_image",htmlTemplate:function(t,e,a,n,s,i){return'<tr><td class="text-center">'+e+'</td><td class="text-center">'+a+'</td><td class="text-center">'+n+'</td><td class="text-center"><a href="'+s+'" target="_blank"><img width="100" height="70" src="'+s+'"/></a></td><td class="text-center"><button type="button" class="btn btn-sm btn-danger" data-text-id="'+e+'">'+i+"</button></td></tr>"},successFunc:function(t,e){var a=e.status,n=e.data;if(1==a){$("#SKU-LIST").parents("table").show();for(var s=0,i=n.length;s<i;s++){var c="/Public/upload/images/wechat/"+n[s].cover_url;$("#SKU-LIST").append(this.htmlTemplate(s,n[s].id,n[s].image_name,n[s].media_id,c,t))}}else $("#EMPTY").show()}}),e=t.LIST,a=t.WINDOW;e.on("click","button",function(){var t=$(this).attr("data-text-id");$("#image_material").val(t),a.modal("hide")})}(window));