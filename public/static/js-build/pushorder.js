$(function(){
    "use strict";
    // 选取sku
    var open_zysku_window = $('#open-zysku-window'), // 选取优惠券按钮
        zysku_selected = $('#zysku-selected'), // 已选列表
        zysku_window = $('#zysku-window'), // 搜索窗口
        search_zysku = $('#search_zysku'), // 关键字
        find_zysku = $('#find-zysku'), // 搜索按钮
        zysku_list = $('#zysku-list'), // 搜索列表
        zysku_empty = $('#zysku-empty'); // 无结果

    var $value_added = $("#value-added");

    // var site_id = $('#site_id').val();

    open_zysku_window.on('click', function(){
        zysku_window.modal('show');
    });

    var first_time = 0;

    zysku_window.on('show.bs.modal', function(){
        if(first_time === 0){
            AJAX_FIND_ZYSKU('', '', '');
            first_time++;
        }
    });

    find_zysku.on('click', function(){
        var type =  $("[name='zy_type']").val();
        var band =  $("[name='zy_band']").val();
        var time =  $("[name='zy_time']").val();

        AJAX_FIND_ZYSKU(type,band,time);
    });

    function AJAX_FIND_ZYSKU(type,band,time){
        $.ajax({
            type: 'POST',
            data: {type:type,band:band,time:time},
            url: '/Ajax/zySkuData',
            dataType: 'JSON',
            beforeSend: function(){
                zysku_list.empty().parents('table').hide();
                zysku_empty.hide();
                find_zysku.prop('disabled', true);
            },
            success: function(JSON){
                var STATUS = JSON.status, DATA = JSON.data;
                if(STATUS == 40001){
                    window.location.reload();
                }else if(STATUS == 0){ // 没找到
                    zysku_empty.show();
                }else if(STATUS == 1){ // 太多
                    toastr.error("查询结果过多，请完善查询内容");
                    search_zysku.focus();
                }else{
                    zysku_list.parents('table').show();
                    for(var i = 0, j = DATA.length; i < j; i++){
                        zysku_list.append( SKU_TPL( DATA[i].zy_code, DATA[i].zy_sku,DATA[i].zy_price,DATA[i].zy_id) );
                    }
                }
            },
            error: function(){
                toastr.error('服务异常，请联系管理人员');
            },
            complete: function(){
                find_zysku.prop('disabled', false);
            }
        });
    }
    var SKU_TPL = function(zy_code,zy_sku,zy_price,zy_id){
        return '<tr>' +
            '<td class="text-center">'+ zy_code +'</td>' +
            '<td>'+ zy_sku +'</td>' +
            '<td>'+ zy_price +'</td>' +
            '<td class="text-center"><button data-id="'+ zy_id +'" data-name="'+ zy_sku +'" data-price="'+ zy_price +'" class="btn btn-xs btn-danger">选择</button></td>' +
            '</tr>';
    };

    var SKU_SELECTED_TPL = function(ID,NAME,PRICE,TYPE){
        return '<tr class="excellent-'+ ID +'" data-type="'+ TYPE +'">' +
            '<td class="text-center">' + (TYPE==0 ? '宽带套餐' : '增值产品' ) + '</td>' +
            '<td>' + NAME + '</td>' +
            ' <td class="text-center">' +
            '<input type="text" name="sku_num[]" class="form-control input-sm text-center" value="1"/>' +
            '</td>' +
            '<td class="text-center">' + PRICE + '</td>' +
            '<th class="text-center col-sm-1">' +
            '<input type="hidden" name="zy_id[]" value="'+ ID +'">' +
            // '<input type="hidden" name="zy_sku_name[]" value="'+ NAME +'">' +
            // '<input type="hidden" name="boss_sku_name[]" value="'+ BOSS +'">' +
            '<a href="javascript:;" class="sku-delete">删除</a>' +
            '</th>' +
            '</tr>';
    };

    zysku_list.on('click', 'button', function(){

        var ID = $(this).data("id"),
            NAME = $(this).data("name"),
            PRICE = $(this).data("price");
            //BOSS = $(this).data("boss");


        if( $('.excellent-' + ID).size() > 0 ){
            toastr.warning('此SKU 已经选择');
        }else{
            zysku_window.modal('hide');
            $value_added.show();
            open_zysku_window.prop('disabled', true);
            zysku_selected.append(SKU_SELECTED_TPL(ID, NAME,PRICE,0));
        }

    });
    //删除选中的sku
    zysku_selected.on('click', '.sku-delete', function(){
        if($(this).parents('tr').data('type') == 0){
            zysku_selected.empty();
            $value_added.hide();
            open_zysku_window.prop('disabled', false);
        }else{
            $(this).parents('tr').remove();
        }
    });

    //重置select
    $('.js_select_reset').on('click',function(){
        $('.js_select_choose').val('0').trigger("chosen:updated");
        $('#zy_band').html('').append('<option value="0">宽带带宽</option>');
        $('#zy_time').html('').append('<option value="0">宽带时长</option>');
        AJAX_FIND_ZYSKU('','','');
    })

    // 赠品
    $value_added.on('click', '.vap', function(){
        var ID = $(this).data('vap-id'),
            NAME = $(this).data('vap-name'),
            PRICE = $(this).data('vap-price');
            //BOSS = $(this).data('vap-boss');

        if( $('.excellent-' + ID).size() > 0 ){
            toastr.warning('此赠品 已经选择');
        }else{
            zysku_selected.append(SKU_SELECTED_TPL(ID, NAME,PRICE,1));
        }
    });


    // 选取社区
    var open_community_window = $('#open-community-window'), // 选取社区按钮
        community_selected = $('#community-selected'), // 已选列表
        community_window = $('#community-window'), // 搜索窗口
        search_community = $('#search_community'), // 关键字
        find_community = $(".find-community"), // 搜索按钮
        community_list = $('#community-list'), // 搜索列表
        community_empty = $('#community-empty'), // 无结果
        community_current_page = $('#community-current-page'),
        community_page = $('#community-page'),
        last_community = $("#last_community"),
        next_community = $("#next_community");


    open_community_window.on('click', function(){
        community_window.modal('show');
    });

    community_window.on('show.bs.modal', function(){
        if(!community_list.children().length) {
            community_empty.show();
        }
    });

    find_community.on('click', function(){
        var searchtype= $(this).attr('data-id');
        var keyword = $.trim(search_community.val());
        if( !keyword.length){
            search_community.focus();
            return false;
        }
        $("#community-searchtype").val(searchtype);
        community_current_page.val(0);
        //var start = community_current_page.val();
        AJAX_FIND_COMMUNITY(keyword,searchtype,0);
    });

    last_community.on('click', function(){
        var searchtype= $("#community-searchtype").val();
        var keyword = $.trim(search_community.val());
        if( !keyword.length){
            search_community.focus();
            return false;
        }
        var num = parseInt(community_current_page.val()) - 20;
        if(num < 0){
            //num = 0;
            last_community.prop('disabled', true);
            return false;
        }else{
            last_community.prop('disabled', false);
        }
        next_community.prop('disabled',false);
        community_current_page.val(num);
        AJAX_FIND_COMMUNITY(keyword,searchtype,num);
    });

    next_community.on('click', function(){
        var searchtype= $("#community-searchtype").val();
        var keyword = $.trim(search_community.val());
        var pagerows=$("#community-pagerows").val();
        if( !keyword.length){
            search_community.focus();
            return false;
        }
        var num = parseInt(community_current_page.val()) + 20;
        if(pagerows<num){
            next_community.prop('disabled',true);
        }else{
            next_community.prop('disabled',false);
        }
        last_community.prop('disabled',false);
        community_current_page.val(num);
        AJAX_FIND_COMMUNITY(keyword,searchtype,num);
    });


    function AJAX_FIND_COMMUNITY(keyword,searchtype,start,limit){
        $.ajax({
            type: 'POST',
            data: {keyword:keyword,searchtype:searchtype,start:start,limit:limit},
            url: '/Ajax/communityInquiry',
            dataType: 'JSON',
            beforeSend: function(){
                community_list.empty().parents('table').hide();
                community_empty.hide();
                find_community.prop('disabled', true);
            },
            success: function(JSON){
                var STATUS = JSON.status, DATA = JSON.data.data,  ROWS = JSON.data.rows;
                if(STATUS == 40001){
                    window.location.reload();
                }else if(STATUS == 0){ // 没找到
                    community_empty.show();
                }else if(STATUS == 1){ // 太多
                    toastr.error("查询结果过多，请完善查询内容");
                    search_community.focus();
                }else{
                    if($('#community-current-page').val()==0 && ROWS <= 20) {
                        community_page.hide();
                    }else {
                        community_page.show();
                    }
                    if(ROWS > 0){
                        community_empty.hide();
                    }else{
                        community_empty.show();
                        return;
                    }
                    community_list.parents('table').show();
                    $("#community-pagerows").val(ROWS);
                    for(var i = 0, j = DATA.length; i < j; i++){
                        community_list.append(COMMUNITY_TPL(
                            DATA[i].bossCommunityAreaId,
                            DATA[i].communityName,
                            DATA[i].bossAddress,
                            DATA[i].servicePointName,
                            DATA[i].regionName,
                            DATA[i].repairPointName,
                            DATA[i].regionId,
                            DATA[i].repairPointId,
                            DATA[i].servicePointId,
                            DATA[i].remark
                        ) );
                    }
                }
            },
            error: function(){
                toastr.error('服务异常，请联系管理人员');
            },
            complete: function(){
                find_community.prop('disabled', false);
            }
        });
    }
    var COMMUNITY_TPL = function(bossCommunityAreaId,communityName,bossAddress,servicePointName,regionName,repairPointName,regionId,repairPointId,servicePointId,remark){

        if(remark == undefined){
            remark='';
        }
        if(repairPointName == undefined){
            repairPointName='';
        }
        if(bossAddress == undefined){
            bossAddress='';
        }

        return '<tr>' +
            '<td class="text-center">'+ communityName +'</td>' +
            //'<td>'+ communityName +'</td>' +
            '<td>'+ bossAddress +'</td>' +
            '<td>'+ regionName +'</td>' +
            '<td>'+ servicePointName +'</td>' +
            '<td>'+ repairPointName +'</td>' +
            //'<td>'+ communityName +'</td>' +
            '<td>'+ remark +'</td>' +
            '<td class="text-center">' +
            '<button data-name="'+ communityName +'"  ' +
            'data-id="'+ bossCommunityAreaId +'" ' +
            'data-address="'+ bossAddress +'" ' +
            'data-service="'+ servicePointName +'" ' +
            'data-region="'+ regionName +'" ' +
            'data-repairpoint="'+ repairPointName +'" ' +
            'data-regionid="'+ regionId +'" ' +
            'data-repairpointid="'+ repairPointId +'" ' +
            'data-servicepointid="'+ servicePointId +'" ' +
            'data-remark="'+ remark +'" ' +
            'class="btn btn-xs btn-danger">选择</button></td>' +
            '</tr>'
            '<div>  </div>'
            ;
    };

    var COMMUNITY_SELECTED_TPL = function(bossCommunityAreaId,communityName,bossAddress,servicePointName,regionName,repairPointName,regionId,repairPointId,servicePointId,remark){
        return '<tr class="community-'+ bossCommunityAreaId +'">' +
            '<td class="text-center">' + communityName + '</td>' +
            '<td>' + bossAddress + '</td>' +
            '<td class="text-center">' + servicePointName + '</td>' +
            '<td class="text-center">' + regionName + '</td>' +
            '<td class="text-center">' + repairPointName + '</td>' +
            '<td class="text-center">' + remark + '</td>' +
            '<th class="text-center col-sm-1">' +
            '<input type="hidden" name="community" value="'+ communityName +'">' +
            '<input type="hidden" name="address" value="'+ bossAddress +'">' +
            '<input type="hidden" name="communityId" value="'+ bossCommunityAreaId +'">' +

            '<input type="hidden" name="bureau" value="'+ regionName +'">' +
            '<input type="hidden" name="bureauCode" value="'+ regionId +'">' +
            '<input type="hidden" name="repairServicehall" value="'+ repairPointName +'">' +
            '<input type="hidden" name="repairServicehallCode" value="'+ repairPointId +'">' +
            '<input type="hidden" name="sellServicehall" value="'+ servicePointName +'">' +
            '<input type="hidden" name="sellServicehallCode" value="'+ servicePointId +'">' +

            '<a href="javascript:;" class="community-delete">删除</a>' +
            '</th>' +
            '</tr>';
    };

    community_list.on('click', 'button', function(){

        var communityName = $(this).data("name"),
            bossCommunityAreaId = $(this).data("id"),
            bossAddress = $(this).data("address"),
            servicePointName = $(this).data("service"),
            regionName = $(this).data("region"),
            repairPointName = $(this).data("repairpoint"),
            regionId = $(this).data("regionid"),
            repairPointId = $(this).data("repairpointid"),
            servicePointId = $(this).data("servicepointid"),
            remark = $(this).data("remark");

            community_window.modal('hide');
            open_community_window.prop('disabled', true);
            community_selected.append(COMMUNITY_SELECTED_TPL(bossCommunityAreaId,communityName,bossAddress,servicePointName,regionName,repairPointName,regionId,repairPointId,servicePointId,remark));

    });

    community_selected.on('click', '.community-delete', function(){
        $(this).parents('tr').remove();
        open_community_window.prop('disabled', false);
    });

    var $elements = $(".sku-delete");
    var len = $elements.length;
    if(len > 0){
        //open_zysku_window.prop('disabled', true);
    }

    var form_flag = 1;
    //推送按钮判断
    $('.js_push_btn').on('click',function(){

        if($("[name='zy_id[]']").length==0) {
            _alert('请选取sku');
            return false;
        }
        if($("[name='communityId']").length == 0){
            _alert('请选取社区');
            return false;
        }
        if($("[name='consult']:selected").val() == ''){
            _alert('请选择流程处理');
            return false;
        }
        if($("[name='appointdate']").val() == ''){
            _alert('请选取预约时间');
            return false;
        }

        swal({
            title: '推送卓越' + "提示",
            text: "确认"+ '推送卓越' +"后不可更改或撤销，请谨慎操作",
            type: "warning",
            showCancelButton: true,
            confirmButtonColor: "#DD6B55",
            confirmButtonText: "确认" + '推送卓越',
            cancelButtonText: "暂不" + '推送卓越',
            closeOnConfirm: false
        }, function(){
            if(form_flag!=1) {
                return false;
            }
            form_flag = 2;
            $('.js_push_excellent').submit();
            $('.sa-button-container .confirm').css('background-color','#D0D0D0');
            $('.sa-button-container .confirm').on('mouseleave',function(){
                $(this).css('background-color','#D0D0D0');
            })
            $('.sa-button-container .confirm').on('mouseenter',function(){
                $(this).css('background-color','#D0D0D0');
            })
        });
    })



});