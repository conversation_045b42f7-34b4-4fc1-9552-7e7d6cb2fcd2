// JavaScript Document
jQuery(function($) {
	// backup fn 
	var _ajax = $.ajax;
	// reset
	$.ajax = function(opt) {
		var _success = opt && opt.success || function(a, b) {};
		var _opt = $.extend(opt, {
			success: function(data, textStatus) {
				if (data.status === 40001) {
					window.location.reload();
					return;
				}
				_success(data, textStatus);
			}
		});
		_ajax(_opt);
	};
});

Date.prototype.Format = function(fmt) {
	var o = {
		"M+": this.getMonth() + 1, //月份 
		"d+": this.getDate(), //日 
		"h+": this.getHours(), //小时 
		"m+": this.getMinutes(), //分 
		"s+": this.getSeconds(), //秒
	};
	if (/(y+)/.test(fmt)) fmt = fmt.replace(RegExp.$1, (this.getFullYear() + "").substr(4 - RegExp.$1.length));
	for (var k in o)
		if (new RegExp("(" + k + ")").test(fmt)) fmt = fmt.replace(RegExp.$1, (RegExp.$1.length == 1) ? (o[k]) : (("00" + o[
			k]).substr(("" + o[k]).length)));
	return fmt;
}

function getCurrentMonthFirst() {
	var date = new Date();
	date.setDate(1);
	return date;
}

function getCurrentMonthLast() {
	var date = new Date();
	var currentMonth = date.getMonth();
	var nextMonth = ++currentMonth;
	var nextMonthFirstDay = new Date(date.getFullYear(), nextMonth, 1);
	var oneDay = 1000 * 60 * 60 * 24;
	return new Date(nextMonthFirstDay - oneDay);
}

function animationHover(o, e) {
	o = $(o),
		o.hover(function() {
			o.addClass('animated ' + e)
		}, function() {
			window.setTimeout(function() {
				o.removeClass('animated ' + e)
			}, 2000)
		})
}

function toDecimal(number) {
	var format = parseFloat(number);
	if (isNaN(format)) {
		return;
	}
	format = Math.round(number * 100) / 100;
	return format;
}

Array.prototype.indexOf = function(val) {
	for (var i = 0; i < this.length; i++) {
		if (this[i] == val) return i;
	}
	return -1;
};

Array.prototype.remove = function(val) {
	var index = this.indexOf(val);
	if (index > -1) {
		this.splice(index, 1);
	}
};

! function(window, undefined) {

	var Region = function() {
			$('#province_id').on('change', function() {
				var province_id = $(this).val(),
					city_id = $('#city_id'),
					district_id = $('#district_id');
				city_id.html('<option value="">选择城市</option>');
				district_id.html('<option value="">选择地区</option>');
				if (province_id != '0' && province_id != '') {
					$.ajax({
						type: 'POST',
						data: 'is_show=1&province_id=' + province_id,
						url: '/Ajax/get_citys',
						dataType: 'JSON',
						success: function(citys) {
							$.each(citys, function(i, v) {
								city_id.append('<option value="' + i + '">' + v + '</option>');
							});
						}
					})
				}
			});

			$('#city_id').on('change', function() {
				var city_id = $(this).val(),
					district_id = $('#district_id');
				district_id.html('<option value="">选择地区</option>');
				if (city_id != '0' && city_id != '') {
					$.ajax({
						type: 'POST',
						data: 'is_show=1&city_id=' + city_id,
						url: '/Ajax/get_districts',
						dataType: 'JSON',
						success: function(districts) {
							$.each(districts, function(i, v) {
								district_id.append('<option value="' + i + '">' + v + '</option>');
							});
						}
					})
				}
			});
		},
		Tree = function() {
			var $e = $(".treeview-collapse");
			$e.delegate(".L1 > .T1", "click", function() {
				$(".L2, .L3").hide();
				$(this).parents("tr").nextUntil(".L1", ".L2").show();
			});
			$e.delegate(".L2 > .T2", "click", function() {
				$(".L3").hide();
				$(this).parents("tr").nextUntil(".L2", ".L3").show();
			});
		},
		Calendar = function() {
			/*
			.calendar 初始化条件
			.datepicker 选日期
			.timepicker 带选择时间
			*/
			if ($("[class*=calendar]").size() > 0) {
				loadScript("/static/js/plugins/layer/laydate/laydate.js", function() { //加载,并执行回调函数					
					$("[class*=calendar]").each(function() {
						$(this).prop('readonly', true);
						if ($(this).val().indexOf('1970-01-01') != -1) {
							$(this).val('');
						}
					});
					lay('.calendar').each(function() {
						if ($(this).hasClass('timepicker')) {
							laydate.render({
								elem: this,
								type: 'datetime',
								trigger: 'click'
							});
						} else if ($(this).hasClass('timeminipicker')) {
							laydate.render({
								elem: this,
								type: 'datetime',
								trigger: 'click',
							});
							var style = document.createElement('link');
							style.href = '/static/css-build/timeminipicker.css';
							style.rel = 'stylesheet';
							style.type = 'text/css';
							$('head').append(style)

						} else {
							laydate.render({
								elem: this,
								trigger: 'click'
							});
						}
					});
				})
			}
		},
		Tooltip = function() {
			if ($(".tooltip-fn").size() > 0) {
				$('.tooltip-fn').tooltip({
					selector: '[data-toggle=tooltip]',
					container: 'body'
				});
				$('.tooltip-fn').popover({
					selector: '[data-toggle=popover]',
					container: 'body'
				});
			}
		},
		Validator = function() {
			if ($("form.validate").size() > 0) {
				$.ajax({
					url: '/static/js/plugins/validate/jquery.validate.min.js',
					dataType: 'script',
					cache: true,
					success: function() {
						$.validator.setDefaults({
							highlight: function(a) {
								$(a).closest(".form-group").addClass("has-error")
							},
							success: function(a) {
								a.closest(".form-group").removeClass("has-error")
							},
							errorElement: "span",
							errorPlacement: function(a, b) {
								if (b.is(":radio") || b.is(":checkbox") || b.parent().is(".input-group")) {
									a.appendTo(b.parent().parent())
								} else {
									a.appendTo(b.parent())
								}
							},
							errorClass: "help-block m-b-none",
							validClass: ""
						});
						$(".validate").validate();
					}
				});
			}
		},
		SweetAlert = function() {
			$('<link/>').attr({
				'rel': 'stylesheet',
				'href': '/static/css/plugins/sweetalert/sweetalert.css'
			}).insertBefore('head > link:last');
			$.ajax({
				url: '/static/js/plugins/sweetalert/sweetalert.min.js',
				dataType: 'script',
				cache: true
			});
		},
		Chosen = function() {
			if ($("select.chosen").size() > 0) {
				$('<link/>').attr({
					'rel': 'stylesheet',
					'href': '/static/css/plugins/chosen/chosen.css'
				}).insertBefore('head > link:last');
				$.ajax({
					url: '/static/js/plugins/chosen/chosen.jquery.min.js',
					dataType: 'script',
					cache: true,
					success: function() {
						$(".chosen").chosen({
							width: "100%",
							search_contains: true,
							no_results_text: "没有查询到相关数据"
						});
					}
				});
			}
		},
		PageControl = function() {
			$("#page-refresh").click(function() {
				window.location.reload();
			});
		},
		CheckBox = function() {
			if ($(".checkbox").size() > 0 || $(".radio").size() > 0) {
				$('<link/>').attr({
					'rel': 'stylesheet',
					'href': '/static/css/plugins/awesome-bootstrap-checkbox/awesome-bootstrap-checkbox.css'
				}).insertBefore('head > link:last');
			}
		},
		CheckAll = function() {
			$('.check-all').on('click', function() {
				$(this).parents('thead').siblings('tbody').find(':checkbox:not(:disabled)').prop('checked', $(this).is(':checked'));
			});
			$('.check-all-form').on("submit", function() {
				if ($(".check-child:checked").size() < 1) {
					_alert("请勾选至少一条数据", "error");
					return false;
				}
			});
		},
		Toastr = function() {
			$('<link/>').attr({
				'rel': 'stylesheet',
				'href': '/static/css/plugins/toastr/toastr.min.css'
			}).insertBefore('head > link:last');
			$.ajax({
				url: '/static/js/plugins/toastr/toastr.min.js',
				dataType: 'script',
				cache: true,
				success: function() {
					toastr.options = {
						"progressBar": true,
						"positionClass": "toast-top-right",
						"showDuration": "500",
						"hideDuration": "500",
						"timeOut": "2000",
						"extendedTimeOut": "500",
						"showEasing": "swing",
						"hideEasing": "linear",
						"showMethod": "fadeIn",
						"hideMethod": "fadeOut"
					};
				}

			});
		},
		PrettyFile = function() {
			if ($('input[type="file"]').size() > 0) {
				$.ajax({
					url: '/static/js/plugins/bootstrap-prettyfile/bootstrap-prettyfile.js',
					dataType: 'script',
					cache: true,
					success: function() {
						$('input[type="file"]').prettyFile({
							text: "选择文件"
						});
					}

				});
			}
		},
		ModalWindow = function() {
			$(".modal").appendTo("body");
		},
		Confirm = function() {
			$(".fn-confirm, .fn-delete, .fn-do").on("click", function(e) {
				var $this = $(this),
					$act = $this.text();
				swal({
						title: $act + "提示",
						text: "确认" + $act + "后不可更改或撤销，请谨慎操作",
						type: "warning",
						showCancelButton: true,
						confirmButtonColor: "#DD6B55",
						confirmButtonText: "确认" + $act,
						cancelButtonText: "暂不" + $act,
						closeOnConfirm: false
					},
					function() {
						window.location.href = $this.attr("data-link");
					});
			});
		},
		InputNumber = function() {
			$('input[type="number"]').on('wheel', function() {
				return false;
			});
		};

	Tree(), Calendar(), Tooltip(), Validator(), SweetAlert(), Chosen(), PageControl(), CheckBox(), CheckAll(), Toastr(),
		PrettyFile(), ModalWindow(), Confirm(), InputNumber(),Region();

}(window);

function _alert(str, type) {
	swal({
		title: "",
		text: str,
		timer: 2000,
		type: type,
		showConfirmButton: false
	});
}

function loadScript(url, callback) {
	var script = document.createElement("script");
	script.type = "text/javascript";
	if (typeof(callback) != "undefined") {
		if (script.readyState) {
			script.onreadystatechange = function() {
				if (script.readyState == "loaded" || script.readyState == "complete") {
					script.onreadystatechange = null;
					callback();
				}
			};
		} else {
			script.onload = function() {
				callback();
			};
		}
	}
	script.src = url;
	document.body.appendChild(script);
}


// 商户对应的经销商
$('.linkage-org').on('change', function () {
    var _id = $(this).val(),
        _tag = $(this).data('tag'),
        $opts = $(this).parents('form').find('.linkage-orgagency');
    if(_id.length){
        $.ajax({
            url: '/Ajax/get_org_has_agency',
            type: 'get',
            data: {
                pid: _id
            },
            success: function (res) {
                if (res.status == 200) {
                    const _data = res.data;
                    $opts.empty();
                    if (_data.length) {
                        if(_tag == 'ao_pitems_all'){
                            $opts.append('<option value="0">请选择</option>')
                        }
                        _data.forEach(function (_items) {
                            $opts.append('<option value="'+_items.agency_id+'">'+(_items.agency_name == '' || _items.agency_name == null ? '默认' : _items.agency_name)+'</option>');
                        });
                    } else {
                        $opts.append('<option value="">默认</option>')
                    }
                    $("select.multiple-select").trigger("chosen:updated");//这个地方不加，就不显示已选中的内容
                } else {
                    _alert(res.info);
                }
            },
            error: function () {
                _alert('网络错误');
            }
        })
    }else{
        $opts.empty().append('<option value="">请选择</option>');
    }
});

