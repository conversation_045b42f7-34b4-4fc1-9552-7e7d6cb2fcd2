$(function(window){
    "use strict";

    var __attrbiute = [];
    var __table = $('#attribute_table'),
        __thead = __table.find('thead'),
        __tbody = __table.find('tbody');

    var _page = $('#createProduct').val();

    // 商品编码
    $('#site_id').on('change', function(){
        var site_id = $(this).val();
        var code = $('input[name="product_code"]');
        var date = new Date();
        if(site_id == 101){
            code.val('KDP-' + date.Format('yyyyMMdd-hms'));
            return;
        }
        if(site_id == 102){
            code.val('CKP-' + date.Format('yyyyMMdd-hms'));
            return;
        }
        code.val('');
    });

    // 读取属性
    var __tab = $('#attr-group-data'),
        __shop_category_id = $('#category_id'),
        __shop_brand_id = $('#brand_id');

    __shop_category_id.on('change',function(){
        // 重置
        __attrbiute = [];
        __tab.empty();
        __thead.empty();
        __tbody.empty();
        __shop_brand_id.html('<option value="">选择品牌</option>');
        // 流程
        if (__shop_category_id.val().length) {
            $.ajax({
                type: 'POST',
                data: 'cat_id=' + $(this).val(),
                url: "/Ajax/get_brands_by_category",
                dataType: 'json',
                success: function(data){
                    var html = '';
                    $.each(data, function(i, v){
                        html += '<option value="' + v.brand_id + '">' + v.brand_name + '</option>';
                    });
                    __shop_brand_id.append(html);
                }
            })
        }
    });

    __shop_brand_id.on('change', function(){
        // 重置
        __attrbiute = [];
        __tab.empty();
        __thead.empty();
        __tbody.empty();
        // 绘制属性表格
        if (__shop_brand_id.val().length) {
            $.ajax({
                type: 'POST',
                data: 'cate_id=' + __shop_category_id.val() + '&brand_id=' + $(this).val(),
                url: '/Ajax/get_attr_group',
                dataType: 'JSON',
                beforeSend: function(){},
                success: function(json){
                    if(json.status == 200){
                        var _data = json.data,
                            _h5 = '';
                        for(var i=0, j=_data.length;i<j;i++){
                            _h5 += '<tr>';
                            _h5 += '<td class="text-center">';
                            _h5 += _data[i].group_name;
                            _h5 += '</td><td>';
                            for (var x=0, y=_data[i].attr.length;x<y;x++) {
                                var __data = _data[i].attr[x];
                                _h5 += '<div class="col-sm-4">';
                                _h5 += '<div class="checkbox checkbox-success">';
                                _h5 += '<input id="check-attr-'+__data.attr_id+'" type="checkbox" name="attr_id[]" class="check-attr" value="'+__data.attr_id+'" data-group-id="'+_data[i].group_id+'" data-group-name="'+_data[i].group_name+'"" data-attr-id="'+__data.attr_id+'" data-attr-name="'+__data.attr_name+'" />';
                                _h5 += '<label for="check-attr-'+__data.attr_id+'">'+__data.attr_name+'</label>';
                                _h5 += '</div>';
                                _h5 += '</div>';
                            }
                            _h5 += '</td>';
                        }
                        __tab.empty().append(_h5);
                    }else{
                        toastr.error(json.info);
                    }
                },
                error: function(){
                    alert('服务异常，请联系管理人员');
                },
                complete: function(){}
            });
        }
    });

    var checked = $('input[name="attr_id[]"]:checked');
    if(checked.size() > 0){
        $.each(checked, function(){
            var _this = $(this),
                __group_id = _this.data('group-id'),
                __group_name = _this.data('group-name'),
                __group = {
                    "group_id" : __group_id,
                    "group_name" : __group_name,
                    "group_attr" : []
                },
                __attr_id = _this.data('attr-id'),
                __attr_name = _this.data('attr-name'),
                __attr = {
                    "attr_id" : __attr_id,
                    "attr_name" : __attr_name
                };

            if(group_in_array(__attrbiute, __group.group_name) == -1){
                __attrbiute.push(__group);
            }
            var gi = group_in_array(__attrbiute, __group.group_name);
            if(attr_in_array(__attrbiute[gi].group_attr, __attr.attr_name) == -1){
                __attrbiute[gi].group_attr.push(__attr);
            }

        });
    }

    $(document).on('click', '.check-attr', function(){
        var _this = $(this),
            __checked = _this.prop('checked'),
            __group_id = _this.data('group-id'),
            __group_name = _this.data('group-name'),
            __group = {
                "group_id" : __group_id,
                "group_name" : __group_name,
                "group_attr" : []
            },
            __attr_id = _this.data('attr-id'),
            __attr_name = _this.data('attr-name'),
            __attr = {
                "attr_id" : __attr_id,
                "attr_name" : __attr_name
            };

        if( __checked ){
            if(_page=='activityProduct') {  //当前是秒杀商品页面
                if($(this).parents('tr').find('.check-attr:checked').size()>1) {
                    _alert('秒杀或拼团商品最多只能有一个SKU','error');
                    $(this).prop('checked',false);
                    return false;
                }
            }
            if(group_in_array(__attrbiute, __group.group_name) == -1){
                __attrbiute.push(__group);
            }
            var gi = group_in_array(__attrbiute, __group.group_name);
            if(attr_in_array(__attrbiute[gi].group_attr, __attr.attr_name) == -1){
                __attrbiute[gi].group_attr.push(__attr);
            }
        }else{
            var gi = group_in_array(__attrbiute, __group.group_name),
                ai = attr_in_array(__attrbiute[gi].group_attr, __attr.attr_name);
            if(ai > -1){
                __attrbiute[gi].group_attr.splice(ai,1);
                if(!__attrbiute[gi].group_attr.length){
                    __attrbiute.splice(gi,1);
                }
            }
        }

        var _head = [], _body = [];
        for(var i = 0, j = __attrbiute.length; i < j; i++){
            var obj_group = __attrbiute[i];
            _head.push([obj_group.group_id,obj_group.group_name]);
            _body.push([]);
            for(var x = 0, y = obj_group.group_attr.length; x < y; x++){
                var temp = [],
                    temp_obj = {
                        "attr_id": obj_group.group_attr[x].attr_id,
                        "attr_name": obj_group.group_attr[x].attr_name,
                        "group_id": obj_group.group_id
                    };
                // temp_obj = obj_group.group_attr[x]
                // temp_obj['group_id'] = obj_group.group_id
                temp.push(temp_obj);
                _body[i].push(temp);
            }

        }

        // console.log(__attrbiute);
        // console.log(exec_recursion(_body));

        // 处理表格
        if(_head.length && _body.length){
            createThead(_head);
            createTbody(exec_recursion(_body));
            resetSku();
        }else{
            __thead.empty();
            __tbody.empty();
        }

    });

    function createThead(arr){
        var _h5 = '<tr>';
        arr.sort(function(a,b){
            return a[0] - b[0];
        });
        for(var i = 0, j = arr.length; i < j; i++){
            _h5 += '<th>'+ arr[i][1] +'</th>';
        }
        _h5 += '<th class="col-sm-2 text-center">提成</th>';
        _h5 += '<th class="col-sm-2 text-center">销售价</th>';
        _h5 += '<th class="col-sm-1 text-center">状态</th>';
        _h5 += '</tr>';
        __thead.empty().append(_h5);
    }

    function createTbody(arr){
        var _h5 = '';
        for(var i = 0, j = arr.length; i < j; i++){
            var ids = "";
            _h5 += '<tr>';
            arr[i].sort(function(a,b){
                return a.group_id - b.group_id;
            });
            for(var x = 0, y = arr[i].length; x < y; x++){
                _h5 += '<td>'+ arr[i][x].attr_name;
                _h5 += '</td>';
                ids = ids_sort(ids, arr[i][x].attr_id);
            }
            _h5 += '<td><input name="costs[]" type="text" class="form-control text-center" value="0" /></td>';
            _h5 += '<td><input name="prices[]" type="text" class="form-control text-center" value="0" /></td>';
            if(_page=='activityProduct'){
                _h5 += '<td class="text-center">可售';
            }else{
                _h5 += '<td class="text-center"><input type="checkbox" name="soldout[]"> 无货';
            }
            _h5 += '<input type="hidden" name="sku_status[]" value="1" />';
            _h5 += '<input type="hidden" class="sku-'+ ids +'" name="attr_ids[]" value="'+ ids +'" />';
            _h5 += '</td>';
            _h5 += '</tr>';
        }
        __tbody.empty().append(_h5);
    }

    function resetSku(){
        var json = $('input[name="items_json"]').val();
        if(json.length){
            try{
                json = JSON.parse(json);
                for(var i = 0, j = json.length; i < j; i++){
                    var elem = __table.find('.sku-' + json[i].attr_ids).parents('tr');
                    elem.find('input[name="costs[]"]').val(json[i].base_price);
                    elem.find('input[name="prices[]"]').val(json[i].shop_price);
                    elem.find('input[name="sku_status[]"]').val(json[i].status);
                    if(json[i].status==2){
                        elem.find('input[name="soldout[]"]').prop('checked', true);
                        elem.addClass('sku-soldout');
                    }
                }
            }catch(e){
                toastr.warning(e.message);
            }
        }
    }

    // 状态
    __tbody.on('click', 'input[name="soldout[]"]', function(){
        var $tr = $(this).parents("tr");
        if( $(this).prop('checked') ){
            $tr.find('input[name="sku_status[]"]').val(2);
            $tr.addClass('sku-soldout');
        }else{
            $tr.find('input[name="sku_status[]"]').val(1);
            $tr.removeClass('sku-soldout');
        }
    });

    // 删除
    __tbody.on('click', 'a.delete', function(){
        if(confirm("是否确认删除此属性商品？")){
            $(this).parents("tr").remove();
        }
    });

});

// 处理ID字符串
function ids_sort(ids, id){
    if(ids.length){
        var arr = ids.indexOf('_') > -1 ? ids.split("_") : [ids];
        arr.push(id);
        return arr.sort().join("_");
    }else{
        return "" + id;
    }
}

// 判断数组内某对象的值
function group_in_array(arr, value){
    return arr.findIndex(function(elem, index){
        return elem.group_name == value;
    });
}

function attr_in_array(arr, value){
    return arr.findIndex(function(elem, index){
        return elem.attr_name == value;
    });
}

// 递归
function exec_recursion(arr){
    var len = arr.length;
    if(len >= 2){
        var len1 = arr[0].length;
        var len2 = arr[1].length;
        var cnt = len1 * len2;
        var items = new Array(cnt);
        var index = 0;
        for(var i = 0; i < len1; i++){
            for(var j = 0; j < len2; j++){
                if(arr[0][i] instanceof Array){
                    items[index] = arr[0][i].concat(arr[1][j]);
                }else{
                    items[index] = [arr[0][i]].concat(arr[1][j]);
                }
                index++;
            }
        }
        var newArr = new Array(len -1);
        for(var i = 2; i < len; i++){
            newArr[i-1] = arr[i];
        }
        newArr[0] = items;
        return exec_recursion(newArr);
    }else{
        return arr[0];
    }
}
