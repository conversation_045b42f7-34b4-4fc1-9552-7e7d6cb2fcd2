$(function(window) {
	"use strict";

	var __table = $('#attribute_table'),
		__thead = __table.find('thead'),
		__tbody = __table.find('tbody');


		$('form').on('submit', function() {
			var sku = __tbody.find('tr');
			if (sku.size() == 0) {
				toastr.error('至少选择一款单品，才能创建商品套餐');
				return false;
			}
			//价格判断
			for (var i = 0, j = sku.size(); i < j; i++) {
				var _s = sku.eq(i).find('input[name="sku_status[]"]'),
					_p = sku.eq(i).find('input[name="prices[]"]');
				if (_s.val() == 1 && _p.val() == 0) {
					toastr.error('商品销售价不能为0');
					$.scrollTo('#tbody', 800);
					return false;
				}
			};
		});


}(window));
