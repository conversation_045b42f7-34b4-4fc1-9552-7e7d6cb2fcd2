$(function() {
	
	var js_goods_img=$('#product_img'),
		js_type = $('.js_type'),
	    js_type_list = $('.js_type_list'),
	    js_selectMsg = $('.js_selectMsg'),
	    js_totlePrice = $('.js_totlePrice'),
	    js_goshop = $('.goshop'),
	    js_arg_details = $('.js_arg_details');
	
	
	chooseType = function(e) {   //选择规格
		if($(e).hasClass('active')) {
			return false;
		}
		$(e).addClass('active').siblings().removeClass('active');
		changeMsg();
	}
	
	function changeMsg () {  //改变已选信息
		var idArr = [];
		js_type.each(function() {
			$(this).find(js_type_list).each(function() {
				if($(this).hasClass('active')) {
					var id = $(this).attr('data-id');
					idArr.push(id);
				}
			})
		})
		var product_id = $("#product_id").val();
		$('#group_id').val(idArr.join('_'));
		$.ajax({
			type: 'POST',
			url: '/Ajax/productPrice',
			data: {"group":idArr,"product_id":product_id},
			dataType: "json",
			beforeSend: function() {
				js_arg_details.off('click');   //发送前解绑
			},
			success: function(data) {
				if(data.status == 200) {
					js_selectMsg.find('span').text(data.data.attrs_name);
					js_totlePrice.text('总计：'+data.data.shop_price+'元');
					js_goshop.attr('disabled',false).removeClass('layui-btn-disabled');
				}else {
					js_goshop.attr('disabled',true).addClass('layui-btn-disabled');
					js_totlePrice.text('暂无商品');
				}
				js_goods_img.attr('src',data.data.sku_img||js_goods_img.attr("data-src"));
			},
			error: function() {
				js_goshop.attr('disabled',true).addClass('layui-btn-disabled');
				js_totlePrice.text('暂无商品');
			},
			complete: function() {
				js_arg_details.on('click','.js_type_list',function(e) {   //事件委托，重新绑定
					chooseType(e.target);
				})
			}
		})
	}
	
	changeMsg();
	
})
