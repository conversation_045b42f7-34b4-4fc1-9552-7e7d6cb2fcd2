/* 
// +----------------------------------------------------------------------
// | JS插件对应样式
// +----------------------------------------------------------------------
*/

/* datepicker */
.ui-datepicker-year, .ui-datepicker-month { font-size: 0.9em !important; color: #333 !important;}

/* switchery */

.switchery {
    background-color: #fff;
    border: 1px solid #dfdfdf;
    border-radius: 20px;
    cursor: pointer;
    display: inline-block;
    height: 30px;
    position: relative;
    vertical-align: middle;
    width: 50px;

    -webkit-box-sizing: content-box;
    -moz-box-sizing: content-box;
    box-sizing: content-box;
}

.switchery > small {
    background: #fff;
    border-radius: 100%;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.4);
    height: 30px;
    position: absolute;
    top: 0;
    width: 30px;
}

/* toast */
#toast-container>.toast:before {
    position: fixed;
    float: left;
    margin: auto .5em auto -1.5em;
    padding-right: .5em;
    color: #FFF;
    font-size: 24px;
    font-family: FontAwesome;
    line-height: 24px;
}

#toast-container>div {
    -webkit-box-shadow: 0 0 3px #999;
    -moz-box-shadow: 0 0 3px #999;
    box-shadow: 0 0 3px #999;
    opacity: .9;
    -ms-filter: alpha(opacity=90);
    filter: alpha(opacity=90);
}

#toast-container>:hover {
    -webkit-box-shadow: 0 0 4px #999;
    -moz-box-shadow: 0 0 4px #999;
    box-shadow: 0 0 4px #999;
    opacity: 1;
    cursor: pointer;
    -ms-filter: alpha(opacity=100);
    filter: alpha(opacity=100);
}

.toast {
    background-color: #1ab394;
}

.toast-success {
    background-color: #1ab394;
}

.toast-error {
    background-color: #ed5565;
}

.toast-info {
    background-color: #23c6c8;
}

.toast-warning {
    background-color: #f8ac59;
}

.toast-top-full-width {
    margin-top: 20px;
}

.toast-bottom-full-width {
    margin-bottom: 20px;
}

/* pace */
.pace {
    -webkit-pointer-events: none;
    pointer-events: none;
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none;
}

.pace-inactive {
    display: none;
}

.pace .pace-progress {
    position: fixed;
    top: 0;
    z-index: 2000;
    width: 100%;
    height: 2px;
    background: #1ab394;
}

.pace-inactive {
    display: none;
}

.pace-done #page-wrapper,.pace-done .footer,.pace-done .nav-header,.pace-done .navbar-static-side,.pace-done li.active {
    -webkit-transition: all .5s;
    -moz-transition: all .5s;
    -o-transition: all .5s;
    transition: all .5s;
}

.pace-done .nav-header {
    transition: all .5s;
}

/* tooltip */
.tooltip-inner {
    padding: 1em;
    background-color: #2f4050;
    text-align: left;
}

.tooltip.top .tooltip-arrow {
    border-top-color: #2f4050;
}

.tooltip.right .tooltip-arrow {
    border-right-color: #2f4050;
}

.tooltip.bottom .tooltip-arrow {
    border-bottom-color: #2f4050;
}

.tooltip.left .tooltip-arrow {
    border-left-color: #2f4050;
}

/* chosen */
.chosen-container-single .chosen-single {
    position: relative;
    overflow: hidden;
    -moz-box-sizing: border-box;
    margin: 0;
    padding: 4px 12px;
    width: 100%;
    height: auto!important;
    min-height: 30px;
    border: 1px solid #e5e6e7;
    border-radius: 1px;
    background: #fff;
    background-color: #fff;
    box-shadow: none;
    cursor: text;
}

.chosen-container-multi .chosen-choices li.search-choice {
    position: relative;
    margin: 3px 0 3px 5px;
    padding: 3px 20px 3px 5px;
    border: 1px solid #ededed;
    border-radius: 2px;
    background: #f1f1f1;
    box-shadow: none;
    color: #333;
    line-height: 13px;
    cursor: default;
}

.chosen-container-multi .chosen-choices{
	border: 1px solid #e5e6e7;
	min-height: 34px;
}

.chosen-container-multi.chosen-container-active .chosen-choices{
	border: 1px solid #1ab394;
	box-shadow:none;
}

select[multiple]::-webkit-scrollbar{
	display: none;
}