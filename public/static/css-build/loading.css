/* 
// +----------------------------------------------------------------------
// | 加载动画
// +----------------------------------------------------------------------
*/

.sk-spinner-rotating-plane.sk-spinner {
	margin: 0 auto;
	width: 30px;
	height: 30px;
	background-color: #1ab394;
	-webkit-animation: sk-rotatePlane 1.2s infinite ease-in-out;
	animation: sk-rotatePlane 1.2s infinite ease-in-out;
}

@-webkit-keyframes sk-rotatePlane {
	0% {
		-webkit-transform: perspective(120px) rotateX(0) rotateY(0);
		transform: perspective(120px) rotateX(0) rotateY(0);
	}

	50% {
		-webkit-transform: perspective(120px) rotateX(-180.1deg) rotateY(0);
		transform: perspective(120px) rotateX(-180.1deg) rotateY(0);
	}

	100% {
		-webkit-transform: perspective(120px) rotateX(-180deg) rotateY(-179.9deg);
		transform: perspective(120px) rotateX(-180deg) rotateY(-179.9deg);
	}
}

@keyframes sk-rotatePlane {
	0% {
		-webkit-transform: perspective(120px) rotateX(0) rotateY(0);
		transform: perspective(120px) rotateX(0) rotateY(0);
	}

	50% {
		-webkit-transform: perspective(120px) rotateX(-180.1deg) rotateY(0);
		transform: perspective(120px) rotateX(-180.1deg) rotateY(0);
	}

	100% {
		-webkit-transform: perspective(120px) rotateX(-180deg) rotateY(-179.9deg);
		transform: perspective(120px) rotateX(-180deg) rotateY(-179.9deg);
	}
}

.sk-spinner-double-bounce.sk-spinner {
	position: relative;
	margin: 0 auto;
	width: 40px;
	height: 40px;
}

.sk-spinner-double-bounce .sk-double-bounce1,.sk-spinner-double-bounce .sk-double-bounce2 {
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	border-radius: 50%;
	background-color: #1ab394;
	opacity: .6;
	-webkit-animation: sk-doubleBounce 2s infinite ease-in-out;
	animation: sk-doubleBounce 2s infinite ease-in-out;
}

.sk-spinner-double-bounce .sk-double-bounce2 {
	-webkit-animation-delay: -1s;
	animation-delay: -1s;
}

@-webkit-keyframes sk-doubleBounce {
	0%,100% {
		-webkit-transform: scale(0);
		transform: scale(0);
	}

	50% {
		-webkit-transform: scale(1);
		transform: scale(1);
	}
}

@keyframes sk-doubleBounce {
	0%,100% {
		-webkit-transform: scale(0);
		transform: scale(0);
	}

	50% {
		-webkit-transform: scale(1);
		transform: scale(1);
	}
}

.sk-spinner-wave.sk-spinner {
	margin: 0 auto;
	width: 50px;
	height: 30px;
	text-align: center;
	font-size: 10px;
}

.sk-spinner-wave div {
	display: inline-block;
	width: 6px;
	height: 100%;
	background-color: #1ab394;
	-webkit-animation: sk-waveStretchDelay 1.2s infinite ease-in-out;
	animation: sk-waveStretchDelay 1.2s infinite ease-in-out;
}

.sk-spinner-wave .sk-rect2 {
	-webkit-animation-delay: -1.1s;
	animation-delay: -1.1s;
}

.sk-spinner-wave .sk-rect3 {
	-webkit-animation-delay: -1s;
	animation-delay: -1s;
}

.sk-spinner-wave .sk-rect4 {
	-webkit-animation-delay: -.9s;
	animation-delay: -.9s;
}

.sk-spinner-wave .sk-rect5 {
	-webkit-animation-delay: -.8s;
	animation-delay: -.8s;
}

@-webkit-keyframes sk-waveStretchDelay {
	0%,100%,40% {
		-webkit-transform: scaleY(.4);
		transform: scaleY(.4);
	}

	20% {
		-webkit-transform: scaleY(1);
		transform: scaleY(1);
	}
}

@keyframes sk-waveStretchDelay {
	0%,100%,40% {
		-webkit-transform: scaleY(.4);
		transform: scaleY(.4);
	}

	20% {
		-webkit-transform: scaleY(1);
		transform: scaleY(1);
	}
}

.sk-spinner-wandering-cubes.sk-spinner {
	position: relative;
	margin: 0 auto;
	width: 32px;
	height: 32px;
}

.sk-spinner-wandering-cubes .sk-cube1,.sk-spinner-wandering-cubes .sk-cube2 {
	position: absolute;
	top: 0;
	left: 0;
	width: 10px;
	height: 10px;
	background-color: #1ab394;
	-webkit-animation: sk-wanderingCubeMove 1.8s infinite ease-in-out;
	animation: sk-wanderingCubeMove 1.8s infinite ease-in-out;
}

.sk-spinner-wandering-cubes .sk-cube2 {
	-webkit-animation-delay: -.9s;
	animation-delay: -.9s;
}

@-webkit-keyframes sk-wanderingCubeMove {
	25% {
		-webkit-transform: translateX(42px) rotate(-90deg) scale(.5);
		transform: translateX(42px) rotate(-90deg) scale(.5);
	}

	50% {
		-webkit-transform: translateX(42px) translateY(42px) rotate(-179deg);
		transform: translateX(42px) translateY(42px) rotate(-179deg);
	}

	50.1% {
		-webkit-transform: translateX(42px) translateY(42px) rotate(-180deg);
		transform: translateX(42px) translateY(42px) rotate(-180deg);
	}

	75% {
		-webkit-transform: translateX(0) translateY(42px) rotate(-270deg) scale(.5);
		transform: translateX(0) translateY(42px) rotate(-270deg) scale(.5);
	}

	100% {
		-webkit-transform: rotate(-360deg);
		transform: rotate(-360deg);
	}
}

@keyframes sk-wanderingCubeMove {
	25% {
		-webkit-transform: translateX(42px) rotate(-90deg) scale(.5);
		transform: translateX(42px) rotate(-90deg) scale(.5);
	}

	50% {
		-webkit-transform: translateX(42px) translateY(42px) rotate(-179deg);
		transform: translateX(42px) translateY(42px) rotate(-179deg);
	}

	50.1% {
		-webkit-transform: translateX(42px) translateY(42px) rotate(-180deg);
		transform: translateX(42px) translateY(42px) rotate(-180deg);
	}

	75% {
		-webkit-transform: translateX(0) translateY(42px) rotate(-270deg) scale(.5);
		transform: translateX(0) translateY(42px) rotate(-270deg) scale(.5);
	}

	100% {
		-webkit-transform: rotate(-360deg);
		transform: rotate(-360deg);
	}
}

.sk-spinner-pulse.sk-spinner {
	margin: 0 auto;
	width: 40px;
	height: 40px;
	border-radius: 100%;
	background-color: #1ab394;
	-webkit-animation: sk-pulseScaleOut 1s infinite ease-in-out;
	animation: sk-pulseScaleOut 1s infinite ease-in-out;
}

@-webkit-keyframes sk-pulseScaleOut {
	0% {
		-webkit-transform: scale(0);
		transform: scale(0);
	}

	100% {
		opacity: 0;
		-webkit-transform: scale(1);
		transform: scale(1);
	}
}

@keyframes sk-pulseScaleOut {
	0% {
		-webkit-transform: scale(0);
		transform: scale(0);
	}

	100% {
		opacity: 0;
		-webkit-transform: scale(1);
		transform: scale(1);
	}
}

.sk-spinner-chasing-dots.sk-spinner {
	position: relative;
	margin: 0 auto;
	width: 40px;
	height: 40px;
	text-align: center;
	-webkit-animation: sk-chasingDotsRotate 2s infinite linear;
	animation: sk-chasingDotsRotate 2s infinite linear;
}

.sk-spinner-chasing-dots .sk-dot1,.sk-spinner-chasing-dots .sk-dot2 {
	position: absolute;
	top: 0;
	display: inline-block;
	width: 60%;
	height: 60%;
	border-radius: 100%;
	background-color: #1ab394;
	-webkit-animation: sk-chasingDotsBounce 2s infinite ease-in-out;
	animation: sk-chasingDotsBounce 2s infinite ease-in-out;
}

.sk-spinner-chasing-dots .sk-dot2 {
	top: auto;
	bottom: 0;
	-webkit-animation-delay: -1s;
	animation-delay: -1s;
}

@-webkit-keyframes sk-chasingDotsRotate {
	100% {
		-webkit-transform: rotate(360deg);
		transform: rotate(360deg);
	}
}

@keyframes sk-chasingDotsRotate {
	100% {
		-webkit-transform: rotate(360deg);
		transform: rotate(360deg);
	}
}

@-webkit-keyframes sk-chasingDotsBounce {
	0%,100% {
		-webkit-transform: scale(0);
		transform: scale(0);
	}

	50% {
		-webkit-transform: scale(1);
		transform: scale(1);
	}
}

@keyframes sk-chasingDotsBounce {
	0%,100% {
		-webkit-transform: scale(0);
		transform: scale(0);
	}

	50% {
		-webkit-transform: scale(1);
		transform: scale(1);
	}
}

.sk-spinner-three-bounce.sk-spinner {
	margin: 0 auto;
	width: 70px;
	text-align: center;
}

.sk-spinner-three-bounce div {
	display: inline-block;
	width: 18px;
	height: 18px;
	border-radius: 100%;
	background-color: #1ab394;
	-webkit-animation: sk-threeBounceDelay 1.4s infinite ease-in-out;
	animation: sk-threeBounceDelay 1.4s infinite ease-in-out;
	-webkit-animation-fill-mode: both;
	animation-fill-mode: both;
}

.sk-spinner-three-bounce .sk-bounce1 {
	-webkit-animation-delay: -.32s;
	animation-delay: -.32s;
}

.sk-spinner-three-bounce .sk-bounce2 {
	-webkit-animation-delay: -.16s;
	animation-delay: -.16s;
}

@-webkit-keyframes sk-threeBounceDelay {
	0%,100%,80% {
		-webkit-transform: scale(0);
		transform: scale(0);
	}

	40% {
		-webkit-transform: scale(1);
		transform: scale(1);
	}
}

@keyframes sk-threeBounceDelay {
	0%,100%,80% {
		-webkit-transform: scale(0);
		transform: scale(0);
	}

	40% {
		-webkit-transform: scale(1);
		transform: scale(1);
	}
}

.sk-spinner-circle.sk-spinner {
	position: relative;
	margin: 0 auto;
	width: 22px;
	height: 22px;
}

.sk-spinner-circle .sk-circle {
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
}

.sk-spinner-circle .sk-circle:before {
	display: block;
	margin: 0 auto;
	width: 20%;
	height: 20%;
	border-radius: 100%;
	background-color: #1ab394;
	content: '';
	-webkit-animation: sk-circleBounceDelay 1.2s infinite ease-in-out;
	animation: sk-circleBounceDelay 1.2s infinite ease-in-out;
	-webkit-animation-fill-mode: both;
	animation-fill-mode: both;
}

.sk-spinner-circle .sk-circle2 {
	-webkit-transform: rotate(30deg);
	transform: rotate(30deg);
	-ms-transform: rotate(30deg);
}

.sk-spinner-circle .sk-circle3 {
	-webkit-transform: rotate(60deg);
	transform: rotate(60deg);
	-ms-transform: rotate(60deg);
}

.sk-spinner-circle .sk-circle4 {
	-webkit-transform: rotate(90deg);
	transform: rotate(90deg);
	-ms-transform: rotate(90deg);
}

.sk-spinner-circle .sk-circle5 {
	-webkit-transform: rotate(120deg);
	transform: rotate(120deg);
	-ms-transform: rotate(120deg);
}

.sk-spinner-circle .sk-circle6 {
	-webkit-transform: rotate(150deg);
	transform: rotate(150deg);
	-ms-transform: rotate(150deg);
}

.sk-spinner-circle .sk-circle7 {
	-webkit-transform: rotate(180deg);
	transform: rotate(180deg);
	-ms-transform: rotate(180deg);
}

.sk-spinner-circle .sk-circle8 {
	-webkit-transform: rotate(210deg);
	transform: rotate(210deg);
	-ms-transform: rotate(210deg);
}

.sk-spinner-circle .sk-circle9 {
	-webkit-transform: rotate(240deg);
	transform: rotate(240deg);
	-ms-transform: rotate(240deg);
}

.sk-spinner-circle .sk-circle10 {
	-webkit-transform: rotate(270deg);
	transform: rotate(270deg);
	-ms-transform: rotate(270deg);
}

.sk-spinner-circle .sk-circle11 {
	-webkit-transform: rotate(300deg);
	transform: rotate(300deg);
	-ms-transform: rotate(300deg);
}

.sk-spinner-circle .sk-circle12 {
	-webkit-transform: rotate(330deg);
	transform: rotate(330deg);
	-ms-transform: rotate(330deg);
}

.sk-spinner-circle .sk-circle2:before {
	-webkit-animation-delay: -1.1s;
	animation-delay: -1.1s;
}

.sk-spinner-circle .sk-circle3:before {
	-webkit-animation-delay: -1s;
	animation-delay: -1s;
}

.sk-spinner-circle .sk-circle4:before {
	-webkit-animation-delay: -.9s;
	animation-delay: -.9s;
}

.sk-spinner-circle .sk-circle5:before {
	-webkit-animation-delay: -.8s;
	animation-delay: -.8s;
}

.sk-spinner-circle .sk-circle6:before {
	-webkit-animation-delay: -.7s;
	animation-delay: -.7s;
}

.sk-spinner-circle .sk-circle7:before {
	-webkit-animation-delay: -.6s;
	animation-delay: -.6s;
}

.sk-spinner-circle .sk-circle8:before {
	-webkit-animation-delay: -.5s;
	animation-delay: -.5s;
}

.sk-spinner-circle .sk-circle9:before {
	-webkit-animation-delay: -.4s;
	animation-delay: -.4s;
}

.sk-spinner-circle .sk-circle10:before {
	-webkit-animation-delay: -.3s;
	animation-delay: -.3s;
}

.sk-spinner-circle .sk-circle11:before {
	-webkit-animation-delay: -.2s;
	animation-delay: -.2s;
}

.sk-spinner-circle .sk-circle12:before {
	-webkit-animation-delay: -.1s;
	animation-delay: -.1s;
}

@-webkit-keyframes sk-circleBounceDelay {
	0%,100%,80% {
		-webkit-transform: scale(0);
		transform: scale(0);
	}

	40% {
		-webkit-transform: scale(1);
		transform: scale(1);
	}
}

@keyframes sk-circleBounceDelay {
	0%,100%,80% {
		-webkit-transform: scale(0);
		transform: scale(0);
	}

	40% {
		-webkit-transform: scale(1);
		transform: scale(1);
	}
}

.sk-spinner-cube-grid.sk-spinner {
	margin: 0 auto;
	width: 30px;
	height: 30px;
}

.sk-spinner-cube-grid .sk-cube {
	float: left;
	width: 33%;
	height: 33%;
	background-color: #1ab394;
	-webkit-animation: sk-cubeGridScaleDelay 1.3s infinite ease-in-out;
	animation: sk-cubeGridScaleDelay 1.3s infinite ease-in-out;
}

.sk-spinner-cube-grid .sk-cube:nth-child(1) {
	-webkit-animation-delay: .2s;
	animation-delay: .2s;
}

.sk-spinner-cube-grid .sk-cube:nth-child(2) {
	-webkit-animation-delay: .3s;
	animation-delay: .3s;
}

.sk-spinner-cube-grid .sk-cube:nth-child(3) {
	-webkit-animation-delay: .4s;
	animation-delay: .4s;
}

.sk-spinner-cube-grid .sk-cube:nth-child(4) {
	-webkit-animation-delay: .1s;
	animation-delay: .1s;
}

.sk-spinner-cube-grid .sk-cube:nth-child(5) {
	-webkit-animation-delay: .2s;
	animation-delay: .2s;
}

.sk-spinner-cube-grid .sk-cube:nth-child(6) {
	-webkit-animation-delay: .3s;
	animation-delay: .3s;
}

.sk-spinner-cube-grid .sk-cube:nth-child(7) {
	-webkit-animation-delay: 0s;
	animation-delay: 0s;
}

.sk-spinner-cube-grid .sk-cube:nth-child(8) {
	-webkit-animation-delay: .1s;
	animation-delay: .1s;
}

.sk-spinner-cube-grid .sk-cube:nth-child(9) {
	-webkit-animation-delay: .2s;
	animation-delay: .2s;
}

@-webkit-keyframes sk-cubeGridScaleDelay {
	0%,100%,70% {
		-webkit-transform: scale3D(1,1,1);
		transform: scale3D(1,1,1);
	}

	35% {
		-webkit-transform: scale3D(0,0,1);
		transform: scale3D(0,0,1);
	}
}

@keyframes sk-cubeGridScaleDelay {
	0%,100%,70% {
		-webkit-transform: scale3D(1,1,1);
		transform: scale3D(1,1,1);
	}

	35% {
		-webkit-transform: scale3D(0,0,1);
		transform: scale3D(0,0,1);
	}
}

.sk-spinner-wordpress.sk-spinner {
	position: relative;
	margin: 0 auto;
	width: 30px;
	height: 30px;
	border-radius: 30px;
	background-color: #1ab394;
	-webkit-animation: sk-innerCircle 1s linear infinite;
	animation: sk-innerCircle 1s linear infinite;
}

.sk-spinner-wordpress .sk-inner-circle {
	position: absolute;
	top: 5px;
	left: 5px;
	display: block;
	width: 8px;
	height: 8px;
	border-radius: 8px;
	background-color: #fff;
}

@-webkit-keyframes sk-innerCircle {
	0% {
		-webkit-transform: rotate(0);
		transform: rotate(0);
	}

	100% {
		-webkit-transform: rotate(360deg);
		transform: rotate(360deg);
	}
}

@keyframes sk-innerCircle {
	0% {
		-webkit-transform: rotate(0);
		transform: rotate(0);
	}

	100% {
		-webkit-transform: rotate(360deg);
		transform: rotate(360deg);
	}
}

.sk-spinner-fading-circle.sk-spinner {
	position: relative;
	margin: 0 auto;
	width: 22px;
	height: 22px;
}

.sk-spinner-fading-circle .sk-circle {
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
}

.sk-spinner-fading-circle .sk-circle:before {
	display: block;
	margin: 0 auto;
	width: 18%;
	height: 18%;
	border-radius: 100%;
	background-color: #1ab394;
	content: '';
	-webkit-animation: sk-circleFadeDelay 1.2s infinite ease-in-out;
	animation: sk-circleFadeDelay 1.2s infinite ease-in-out;
	-webkit-animation-fill-mode: both;
	animation-fill-mode: both;
}

.sk-spinner-fading-circle .sk-circle2 {
	-webkit-transform: rotate(30deg);
	transform: rotate(30deg);
	-ms-transform: rotate(30deg);
}

.sk-spinner-fading-circle .sk-circle3 {
	-webkit-transform: rotate(60deg);
	transform: rotate(60deg);
	-ms-transform: rotate(60deg);
}

.sk-spinner-fading-circle .sk-circle4 {
	-webkit-transform: rotate(90deg);
	transform: rotate(90deg);
	-ms-transform: rotate(90deg);
}

.sk-spinner-fading-circle .sk-circle5 {
	-webkit-transform: rotate(120deg);
	transform: rotate(120deg);
	-ms-transform: rotate(120deg);
}

.sk-spinner-fading-circle .sk-circle6 {
	-webkit-transform: rotate(150deg);
	transform: rotate(150deg);
	-ms-transform: rotate(150deg);
}

.sk-spinner-fading-circle .sk-circle7 {
	-webkit-transform: rotate(180deg);
	transform: rotate(180deg);
	-ms-transform: rotate(180deg);
}

.sk-spinner-fading-circle .sk-circle8 {
	-webkit-transform: rotate(210deg);
	transform: rotate(210deg);
	-ms-transform: rotate(210deg);
}

.sk-spinner-fading-circle .sk-circle9 {
	-webkit-transform: rotate(240deg);
	transform: rotate(240deg);
	-ms-transform: rotate(240deg);
}

.sk-spinner-fading-circle .sk-circle10 {
	-webkit-transform: rotate(270deg);
	transform: rotate(270deg);
	-ms-transform: rotate(270deg);
}

.sk-spinner-fading-circle .sk-circle11 {
	-webkit-transform: rotate(300deg);
	transform: rotate(300deg);
	-ms-transform: rotate(300deg);
}

.sk-spinner-fading-circle .sk-circle12 {
	-webkit-transform: rotate(330deg);
	transform: rotate(330deg);
	-ms-transform: rotate(330deg);
}

.sk-spinner-fading-circle .sk-circle2:before {
	-webkit-animation-delay: -1.1s;
	animation-delay: -1.1s;
}

.sk-spinner-fading-circle .sk-circle3:before {
	-webkit-animation-delay: -1s;
	animation-delay: -1s;
}

.sk-spinner-fading-circle .sk-circle4:before {
	-webkit-animation-delay: -.9s;
	animation-delay: -.9s;
}

.sk-spinner-fading-circle .sk-circle5:before {
	-webkit-animation-delay: -.8s;
	animation-delay: -.8s;
}

.sk-spinner-fading-circle .sk-circle6:before {
	-webkit-animation-delay: -.7s;
	animation-delay: -.7s;
}

.sk-spinner-fading-circle .sk-circle7:before {
	-webkit-animation-delay: -.6s;
	animation-delay: -.6s;
}

.sk-spinner-fading-circle .sk-circle8:before {
	-webkit-animation-delay: -.5s;
	animation-delay: -.5s;
}

.sk-spinner-fading-circle .sk-circle9:before {
	-webkit-animation-delay: -.4s;
	animation-delay: -.4s;
}

.sk-spinner-fading-circle .sk-circle10:before {
	-webkit-animation-delay: -.3s;
	animation-delay: -.3s;
}

.sk-spinner-fading-circle .sk-circle11:before {
	-webkit-animation-delay: -.2s;
	animation-delay: -.2s;
}

.sk-spinner-fading-circle .sk-circle12:before {
	-webkit-animation-delay: -.1s;
	animation-delay: -.1s;
}

@-webkit-keyframes sk-circleFadeDelay {
	0%,100%,39% {
		opacity: 0;
	}

	40% {
		opacity: 1;
	}
}

@keyframes sk-circleFadeDelay {
	0%,100%,39% {
		opacity: 0;
	}

	40% {
		opacity: 1;
	}
}