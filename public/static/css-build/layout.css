/* 
// +----------------------------------------------------------------------
// | 页面布局
// +----------------------------------------------------------------------
*/

.flex-center{
	display: flex;
	border-bottom: 2px solid rgb(228, 228, 228);
}

#login-page .tab-box{
	align-items: flex-end;
	justify-content: center;
	padding-bottom: 8px;
	margin-bottom: 3px;
}
#login-page .tab-box div{
	position: relative;
	cursor: pointer;
}
#login-page .tab-box div~div{
	margin-left: 30px;
}
#login-page .tab-box div.active{
	color: #1a7bb9;
	font-size: 16px;
}
#login-page .tab-box div.active::after{
	content: '';
	position: absolute;
	bottom: -11px;
	left: 50%;
	margin-left: -50%;
	width: 100%;
	height: 3px;
	border-radius: 3px;
	background-color: #1a7bb9;
}

.logo-name {
	margin-bottom: 0;
	color: #e6e6e6;
	letter-spacing: -10px;
	font-weight: 800;
	font-size: 120px!important;
}

.logo-element {
	display: none;
	padding: 18px 0;
	color: #fff;
	text-align: center;
	font-weight: 600;
	font-size: 18px;
}


.slimScrollDiv>* {
	overflow: hidden;
}

#page-wrapper {
	min-height: auto;
}

#wrapper {
	overflow-x: hidden;
	width: 100%;
	background-color: #2f4050;
}

.wrapper {
	padding: 15px;
}

.wrapper-content {
	padding: 15px;
}

#page-wrapper {
	position: inherit;
	margin: 0 0 0 170px;
	padding: 0 15px;
}

#content-main {
	overflow: hidden;
	height: calc(100% - 130px);
}


/* J_menu */
.J_menuTab {
	-webkit-transition: all .3s ease-out 0s;
	transition: all .3s ease-out 0s;
}

::-webkit-scrollbar-track {
	background-color: #f5f5f5;
}

::-webkit-scrollbar {
	width: 6px;
	background-color: #f5f5f5;
}

::-webkit-scrollbar-thumb {
	background-color: #999;
}

/* login */
#login-page {
    overflow: hidden;
}

#login-page .middle-box {
    width: 320px;
    padding-top: 60px;
    float: right;
}

#login-main {
    height: 450px;
}

@media (max-width: 992px) {
    #login-page {
        background-color: #1c84c6;
    }

    #login-page .middle-box {
        float: none;
    }

    #login-page .bottom_msg {
        display: none;
    }
}

@media (min-width: 992px) {
    #login-main {
        background: url(/static/img/across_login.jpg) center center no-repeat rgb(255, 255, 255);
    }
}

#login-page .login-box {
    padding: 20px;
    border-radius: 10px;
    background: #fff;
}

#login-page .login-box .form-group {
    padding-left: 34px;
    position: relative;
}

#login-page .login-box .form-group .icon {
    position: absolute;
    top: 0;
    left: 0;
    width: 34px;
    height: 100%;
    text-align: center;
    line-height: 34px;
    background-color: #f5f7fa;
    color: #909399;
    border: 1px solid #e5e6e7;
    border-right: none;
    border-top-left-radius: 1px;
    border-bottom-left-radius: 1px;
}

#login-header {
    width: 1170px;
    margin: 60px auto 0;
    padding-bottom: 15px;
}

#login-header .topLink {
    position: relative;
    top: 47px;
    color: #ccc;
}

#login-header .topLink a {
    margin: 0 10px;
    color: #666;
}

#login-main .main-content {
    max-width: 1170px;
    margin: 0 auto;
    height: 100%;
}

.vcode-box {
	position: relative;
}

.vcode-reset {
	position: absolute;
	top: 4px;
	right: 4px;
	cursor: pointer;
}

/* fixed icon */
.fixed-icon {
	position: fixed;
	top: 5px;
	left: 5px;
	z-index: 100;
}

.fixed-icon a {
	display: block;
	width: 30px;
	height: 30px;
	line-height: 30px;
	border-radius: 10%;
	background: #2f4050;
	color: #fff;
	text-align: center;
	opacity: .5;
}

.fixed-icon a:hover {
	opacity: 1;
}

/* menu arrow */
.arrow {
	float: right;
	margin-top: 2px;
}

.fa.arrow:before {
	content: "\f104";
}

.active>a>.fa.arrow:before {
	content: "\f107";
}

/* footer */

.footer {
	overflow: hidden;
	margin: 0 -15px;
	padding: 10px 20px;
	height: 36px;
	border-top: 1px solid #e7eaec;
	background: none repeat scroll 0 0 #fff;
}

.footer.fixed_full {
	position: fixed;
	right: 0;
	bottom: 0;
	left: 0;
	z-index: 1000;
	padding: 10px 20px;
	border-top: 1px solid #e7eaec;
	background: #fff;
}

.footer.fixed {
	position: fixed;
	right: 0;
	bottom: 0;
	left: 0;
	z-index: 1000;
	margin-left: 220px;
	padding: 10px 20px;
	border-top: 1px solid #e7eaec;
	background: #fff;
}

/* pager */
.pager a {
	display: inline-block;
	margin:0 2px;
	padding: 3px 9px;
	border: 1px solid #1ab394;
	border-radius: 3px;
	background: #1ab394;
	color: #fff;
}

.pager .current {
	border-color: #ed5565;
	background: #ed5565;
}

/* treeview */
.treeview .T1 i,.treeview .T2 i,.treeview .T3 i {
	font-size: 13px;
}

.treeview .T2 {
	text-indent: 1em;
}

.treeview .T2 i {
	font-size: 13px;
}

.treeview .T3 {
	text-indent: 2em;
}

/* middle box */
.middle-box {
	z-index: 100;
	margin: 0 auto;
	padding-top: 40px;
	max-width: 400px;
}

.middle-box h1 {
	font-size: 170px;
}

.wrapper .middle-box {
	margin-top: 140px;
}

/* linkblock */
.link-block {
	padding: 10px;
	font-size: 12px;
}

.nav.navbar-top-links .link-block a {
	font-size: 12px;
}

.link-block a {
	color: inherit;
	font-size: 10px;
}


/* define */
#page-goback,#page-refresh { color: #ed5565;}
    
    
