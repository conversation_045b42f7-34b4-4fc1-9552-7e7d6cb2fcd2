/* 
// +----------------------------------------------------------------------
// | HTML默认样式重置
// +----------------------------------------------------------------------
*/

h1,h2,h3,h4,h5,h6 {
	font-weight: 100;
}

h1 {
	font-size: 30px;
}

h2 {
	font-size: 24px;
}

h3 {
	font-size: 16px;
}

h4 {
	font-size: 14px;
}

h5 {
	font-size: 12px;
}

h6 {
	font-size: 10px;
}

h3,h4,h5 {
	margin-top: 5px;
	font-weight: 600;
}

ins {
	background-color: #c6ffc6;
	text-decoration: none;
}

del {
	background-color: #ffc6c6;
}


a {
	cursor: pointer;
}

a:focus,a:hover {
	text-decoration: none;
}

body {
	overflow-x: hidden;
	color: #676a6c;
	font-size: 13px;
	font-family: "Open Sans","Helvetica Neue","Helvetica","Verdana","Tahoma";
}

body,html {
	height: 100%;
}

input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button{
    -webkit-appearance: none !important;
    margin: 0; 
}
input[type="number"]{
	-moz-appearance:textfield;
}
