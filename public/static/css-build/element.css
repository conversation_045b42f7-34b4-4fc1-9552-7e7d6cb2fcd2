/* 
// +----------------------------------------------------------------------
// | 元素模块
// +----------------------------------------------------------------------
*/

/* widget */
.widget {
    border-radius: 5px;
    margin: 10px 0;
    padding: 15px 20px;
}

.widget h2 {
    font-size: 30px;
}

.widget h2, .widget h3 {
    margin-top: 5px;
    margin-bottom: 0;
}

/* progress */

.progress-bar {
	background-color: #1ab394;
}

.progress-small,.progress-small .progress-bar {
	height: 10px;
}

.progress-mini,.progress-small {
	margin-top: 5px;
}

.progress-mini,.progress-mini .progress-bar {
	margin-bottom: 0;
	height: 5px;
}

.progress-bar-navy-light {
	background-color: #3dc7ab;
}

.progress-bar-success {
	background-color: #1c84c6;
}

.progress-bar-info {
	background-color: #23c6c8;
}

.progress-bar-warning {
	background-color: #f8ac59;
}

.progress-bar-danger {
	background-color: #ed5565;
}

/* panel */

.panel-primary {
	border-color: #1ab394;
}

.panel-primary>.panel-heading {
	border-color: #1ab394;
	background-color: #1ab394;
}

.panel-success {
	border-color: #1c84c6;
}

.panel-success>.panel-heading {
	border-color: #1c84c6;
	background-color: #1c84c6;
	color: #fff;
}

.panel-info {
	border-color: #23c6c8;
}

.panel-info>.panel-heading {
	border-color: #23c6c8;
	background-color: #23c6c8;
	color: #fff;
}

.panel-warning {
	border-color: #f8ac59;
}

.panel-warning>.panel-heading {
	border-color: #f8ac59;
	background-color: #f8ac59;
	color: #fff;
}

.panel-danger {
	border-color: #ed5565;
}

.panel-danger>.panel-heading {
	border-color: #ed5565;
	background-color: #ed5565;
	color: #fff;
}

.panel-title {
	font-size: inherit;
}

.panel.blank-panel {
	margin: 0;
	background: 0;
}

.blank-panel .panel-heading {
	padding-bottom: 0;
}

/* ibox */
.ibox {
	clear: both;
	margin-top: 0;
	margin-bottom: 25px;
	padding: 0;
}

.ibox.collapsed .ibox-content {
	display: none;
}

.ibox.collapsed .fa.fa-chevron-up:before {
	content: "\f078";
}

.ibox.collapsed .fa.fa-chevron-down:before {
	content: "\f077";
}

.ibox:after,.ibox:before {
	display: table;
}

.ibox-title {
	margin-bottom: 0;
	padding: 10px 15px;
	min-height: 48px;
	line-height: 24px;
	border-color: #e7eaec;
	border-style: solid solid none;
	border-width: 4px 0 0;
	border-image: none;
	background-color: #fff;
	color: inherit;
	-moz-border-bottom-colors: none;
	-moz-border-left-colors: none;
	-moz-border-right-colors: none;
	-moz-border-top-colors: none;
}

.ibox-content {
	padding: 15px;
	border-color: #e7eaec;
	border-style: solid solid none;
	border-width: 1px 0;
	border-image: none;
	background-color: #fff;
	color: inherit;
}

.ibox-content {
	clear: both;
}

.ibox-heading {
	border-bottom: 0;
	background-color: #f3f6fb;
}

.ibox-heading h3 {
	font-weight: 200;
	font-size: 24px;
}

.ibox-title h5 {
	float: left;
	display: inline-block;
	line-height: 24px;
	margin: 0!important;
	padding: 0;
	text-overflow: ellipsis;
	font-size: 13px;
}

.ibox-title .label {
	float: left;
	margin-left: 4px;
}

.ibox-tools {
	position: relative;
	float: right;
	display: inline-block;
	margin-top: 0;
	padding: 0;
}

.ibox-tools a {
	margin-left: 5px;
	color: #c4c4c4;
	cursor: pointer;
}

.ibox-tools a.btn-primary {
	color: #fff;
}

.ibox-tools .dropdown-menu>li>a {
	padding: 4px 10px;
	font-size: 12px;
}

.ibox .open>.dropdown-menu {
	right: 0;
	left: auto;
}

.ibox-content h1,.ibox-content h2,.ibox-content h3,.ibox-content h4,.ibox-content h5,.ibox-title h1,.ibox-title h2,.ibox-title h3,.ibox-title h4,.ibox-title h5 {
	margin-top: 5px;
}

.ibox-content.text-box {
	padding-top: 15px;
	padding-bottom: 0;
}

/* timeline */
.vertical-container {
	margin: 0 auto;
	width: 90%;
	max-width: 1170px;
}

.vertical-container::after {
	clear: both;
	display: table;
	content: '';
}

#vertical-timeline {
	position: relative;
	margin-top: 2em;
	margin-bottom: 2em;
	padding: 0;
}

#vertical-timeline::before {
	position: absolute;
	top: 0;
	left: 18px;
	width: 4px;
	height: 100%;
	background: #f1f1f1;
	content: '';
}

.vertical-timeline-content .btn {
	float: right;
}

#vertical-timeline.light-timeline:before {
	background: #e7eaec;
}

.dark-timeline .vertical-timeline-content:before {
    border-color: transparent #f5f5f5 transparent transparent;
}

.dark-timeline.center-orientation .vertical-timeline-content:before {
    border-color: transparent transparent transparent #f5f5f5;
}

.dark-timeline .vertical-timeline-block:nth-child(2n) .vertical-timeline-content:before, .dark-timeline.center-orientation .vertical-timeline-block:nth-child(2n) .vertical-timeline-content:before {
    border-color: transparent #f5f5f5 transparent transparent;
}

.dark-timeline .vertical-timeline-content, .dark-timeline.center-orientation .vertical-timeline-content {
    background: #f5f5f5;
}

.vertical-timeline-block {
	position: relative;
	margin: 2em 0;
}

.vertical-timeline-block:after {
	clear: both;
	display: table;
	content: "";
}

.vertical-timeline-block:first-child {
	margin-top: 0;
}

.vertical-timeline-block:last-child {
	margin-bottom: 0;
}

.vertical-timeline-icon {
	position: absolute;
	top: 0;
	left: 0;
	width: 40px;
	height: 40px;
	border: 3px solid #f1f1f1;
	border-radius: 50%;
	text-align: center;
	font-size: 16px;
}

.vertical-timeline-icon i {
	position: relative;
	top: 50%;
	left: 50%;
	display: block;
	margin-top: -9px;
	margin-left: -12px;
	width: 24px;
	height: 24px;
}

.vertical-timeline-content {
	position: relative;
	margin-left: 60px;
	padding: 1em;
	border-radius: .25em;
	background: #fff;
}

.vertical-timeline-content:after {
	clear: both;
	display: table;
	content: "";
}

.vertical-timeline-content h2 {
	margin-top: 4px;
	font-weight: 400;
}

.vertical-timeline-content p {
	margin: 1em 0;
	line-height: 1.6;
}

.vertical-timeline-content .vertical-date {
	float: left;
	font-weight: 500;
}

.vertical-date small {
	color: #1ab394;
	font-weight: 400;
}

.vertical-timeline-content::before {
	position: absolute;
	top: 16px;
	right: 100%;
	width: 0;
	height: 0;
	border: 7px solid transparent;
	border-right: 7px solid #fff;
	content: '';
}

/* modal */
.inmodal .modal-header {
	border-bottom: none;
    text-align: center;
}

.inmodal .modal-title {
    font-size: 26px;
}
.inmodal .modal-icon {
    color: #e2e3e3;
    font-size: 84px;
}
.modal-footer {
    margin-top: 0;
}

/* dropdown */
.dropdown-menu {
	position: absolute;
	top: 100%;
	left: 0;
	z-index: 1000;
	float: left;
	display: none;
	padding: 0;
	border: medium none;
	border-radius: 0;
	box-shadow: 0 0 3px rgba(86,96,117,.3);
	list-style: none outside none;
	text-shadow: none;
	font-size: 12px;
}

.dropdown-menu>li>a {
	margin: 4px;
	border-radius: 3px;
	color: inherit;
	text-align: left;
	font-weight: 400;
	line-height: 25px;
}

.dropdown-menu>li>a.font-bold {
	font-weight: 600;
}

.dropdown-alerts {
	padding: 10px 10px 10px 10px;
}

.dropdown-alerts li a {
	font-size: 12px;
}

.dropdown-alerts li em {
	font-size: 10px;
}

/* right-top corner  */
.count-info .label {
	position: absolute;
	top: 12px;
	right: 6px;
	padding: 1px 5px;
	line-height: 12px;
}
